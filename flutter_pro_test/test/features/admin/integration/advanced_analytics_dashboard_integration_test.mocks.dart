// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/admin/integration/advanced_analytics_dashboard_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:dartz/dartz.dart' as _i4;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i11;
import 'package:flutter_pro_test/features/admin/data/services/realtime_analytics_service.dart'
    as _i5;
import 'package:flutter_pro_test/features/admin/data/services/websocket_service.dart'
    as _i9;
import 'package:flutter_pro_test/features/admin/domain/entities/booking_analytics.dart'
    as _i8;
import 'package:flutter_pro_test/features/admin/domain/entities/partner_analytics.dart'
    as _i13;
import 'package:flutter_pro_test/features/admin/domain/entities/report_config.dart'
    as _i16;
import 'package:flutter_pro_test/features/admin/domain/entities/revenue_analytics.dart'
    as _i15;
import 'package:flutter_pro_test/features/admin/domain/entities/system_metrics.dart'
    as _i7;
import 'package:flutter_pro_test/features/admin/domain/entities/user_analytics.dart'
    as _i14;
import 'package:flutter_pro_test/features/admin/domain/repositories/admin_repository.dart'
    as _i3;
import 'package:flutter_pro_test/features/admin/domain/repositories/analytics_repository.dart'
    as _i2;
import 'package:flutter_pro_test/features/admin/domain/usecases/get_booking_analytics.dart'
    as _i12;
import 'package:flutter_pro_test/features/admin/domain/usecases/get_system_metrics.dart'
    as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAnalyticsRepository_0 extends _i1.SmartFake
    implements _i2.AnalyticsRepository {
  _FakeAnalyticsRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAdminRepository_1 extends _i1.SmartFake
    implements _i3.AdminRepository {
  _FakeAdminRepository_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_2<L, R> extends _i1.SmartFake implements _i4.Either<L, R> {
  _FakeEither_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [RealtimeAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRealtimeAnalyticsService extends _i1.Mock
    implements _i5.RealtimeAnalyticsService {
  MockRealtimeAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void initialize() => super.noSuchMethod(
    Invocation.method(#initialize, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Stream<_i7.SystemMetrics> getSystemMetricsStream() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemMetricsStream, []),
            returnValue: _i6.Stream<_i7.SystemMetrics>.empty(),
          )
          as _i6.Stream<_i7.SystemMetrics>);

  @override
  _i6.Stream<_i8.BookingAnalytics> getBookingAnalyticsStream() =>
      (super.noSuchMethod(
            Invocation.method(#getBookingAnalyticsStream, []),
            returnValue: _i6.Stream<_i8.BookingAnalytics>.empty(),
          )
          as _i6.Stream<_i8.BookingAnalytics>);

  @override
  _i6.Stream<double> getRevenueStream() =>
      (super.noSuchMethod(
            Invocation.method(#getRevenueStream, []),
            returnValue: _i6.Stream<double>.empty(),
          )
          as _i6.Stream<double>);

  @override
  _i6.Stream<int> getUserCountStream() =>
      (super.noSuchMethod(
            Invocation.method(#getUserCountStream, []),
            returnValue: _i6.Stream<int>.empty(),
          )
          as _i6.Stream<int>);

  @override
  _i6.Stream<int> getActiveBookingsStream() =>
      (super.noSuchMethod(
            Invocation.method(#getActiveBookingsStream, []),
            returnValue: _i6.Stream<int>.empty(),
          )
          as _i6.Stream<int>);

  @override
  _i6.Stream<Map<String, int>> getPartnerStatusStream() =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerStatusStream, []),
            returnValue: _i6.Stream<Map<String, int>>.empty(),
          )
          as _i6.Stream<Map<String, int>>);
}

/// A class which mocks [WebSocketService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebSocketService extends _i1.Mock implements _i9.WebSocketService {
  MockWebSocketService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isConnected =>
      (super.noSuchMethod(Invocation.getter(#isConnected), returnValue: false)
          as bool);

  @override
  _i6.Stream<Map<String, dynamic>> get messageStream =>
      (super.noSuchMethod(
            Invocation.getter(#messageStream),
            returnValue: _i6.Stream<Map<String, dynamic>>.empty(),
          )
          as _i6.Stream<Map<String, dynamic>>);

  @override
  _i6.Future<void> connect({String? endpoint}) =>
      (super.noSuchMethod(
            Invocation.method(#connect, [], {#endpoint: endpoint}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void disconnect() => super.noSuchMethod(
    Invocation.method(#disconnect, []),
    returnValueForMissingStub: null,
  );

  @override
  void sendMessage(Map<String, dynamic>? message) => super.noSuchMethod(
    Invocation.method(#sendMessage, [message]),
    returnValueForMissingStub: null,
  );

  @override
  void subscribeToAnalytics(List<String>? eventTypes) => super.noSuchMethod(
    Invocation.method(#subscribeToAnalytics, [eventTypes]),
    returnValueForMissingStub: null,
  );

  @override
  void unsubscribeFromAnalytics(List<String>? eventTypes) => super.noSuchMethod(
    Invocation.method(#unsubscribeFromAnalytics, [eventTypes]),
    returnValueForMissingStub: null,
  );

  @override
  void requestRealtimeData(String? dataType) => super.noSuchMethod(
    Invocation.method(#requestRealtimeData, [dataType]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [GetSystemMetrics].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetSystemMetricsUseCase extends _i1.Mock
    implements _i10.GetSystemMetrics {
  MockGetSystemMetricsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AnalyticsRepository get analyticsRepository =>
      (super.noSuchMethod(
            Invocation.getter(#analyticsRepository),
            returnValue: _FakeAnalyticsRepository_0(
              this,
              Invocation.getter(#analyticsRepository),
            ),
          )
          as _i2.AnalyticsRepository);

  @override
  _i3.AdminRepository get adminRepository =>
      (super.noSuchMethod(
            Invocation.getter(#adminRepository),
            returnValue: _FakeAdminRepository_1(
              this,
              Invocation.getter(#adminRepository),
            ),
          )
          as _i3.AdminRepository);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>> call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>>.value(
                  _FakeEither_2<_i11.Failure, _i7.SystemMetrics>(
                    this,
                    Invocation.method(#call, []),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>>);
}

/// A class which mocks [GetBookingAnalytics].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetBookingAnalyticsUseCase extends _i1.Mock
    implements _i12.GetBookingAnalytics {
  MockGetBookingAnalyticsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AnalyticsRepository get analyticsRepository =>
      (super.noSuchMethod(
            Invocation.getter(#analyticsRepository),
            returnValue: _FakeAnalyticsRepository_0(
              this,
              Invocation.getter(#analyticsRepository),
            ),
          )
          as _i2.AnalyticsRepository);

  @override
  _i3.AdminRepository get adminRepository =>
      (super.noSuchMethod(
            Invocation.getter(#adminRepository),
            returnValue: _FakeAdminRepository_1(
              this,
              Invocation.getter(#adminRepository),
            ),
          )
          as _i3.AdminRepository);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i8.BookingAnalytics>> call(
    _i12.GetBookingAnalyticsParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i8.BookingAnalytics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i8.BookingAnalytics>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i8.BookingAnalytics>>);
}

/// A class which mocks [AnalyticsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsRepo extends _i1.Mock implements _i2.AnalyticsRepository {
  MockAnalyticsRepo() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>> getSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemMetrics, []),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>>.value(
                  _FakeEither_2<_i11.Failure, _i7.SystemMetrics>(
                    this,
                    Invocation.method(#getSystemMetrics, []),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i7.SystemMetrics>>);

  @override
  _i6.Stream<_i4.Either<_i11.Failure, _i7.SystemMetrics>>
  watchSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemMetrics, []),
            returnValue:
                _i6.Stream<_i4.Either<_i11.Failure, _i7.SystemMetrics>>.empty(),
          )
          as _i6.Stream<_i4.Either<_i11.Failure, _i7.SystemMetrics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i8.BookingAnalytics>>
  getBookingAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
    String? partnerId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
              #partnerId: partnerId,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i8.BookingAnalytics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i8.BookingAnalytics>(
                    this,
                    Invocation.method(#getBookingAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #serviceId: serviceId,
                      #partnerId: partnerId,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i8.BookingAnalytics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i13.PartnerAnalytics>>
  getPartnerAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includePerformanceDetails = false,
    bool? includeQualityMetrics = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includePerformanceDetails: includePerformanceDetails,
              #includeQualityMetrics: includeQualityMetrics,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i13.PartnerAnalytics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i13.PartnerAnalytics>(
                    this,
                    Invocation.method(#getPartnerAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includePerformanceDetails: includePerformanceDetails,
                      #includeQualityMetrics: includeQualityMetrics,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i13.PartnerAnalytics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i14.UserAnalytics>> getUserAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeCohortAnalysis = false,
    bool? includeSegmentation = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeCohortAnalysis: includeCohortAnalysis,
              #includeSegmentation: includeSegmentation,
            }),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i14.UserAnalytics>>.value(
                  _FakeEither_2<_i11.Failure, _i14.UserAnalytics>(
                    this,
                    Invocation.method(#getUserAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeCohortAnalysis: includeCohortAnalysis,
                      #includeSegmentation: includeSegmentation,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i14.UserAnalytics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i15.RevenueAnalytics>>
  getRevenueAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeForecasts = false,
    bool? includeComparisons = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRevenueAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeForecasts: includeForecasts,
              #includeComparisons: includeComparisons,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i15.RevenueAnalytics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i15.RevenueAnalytics>(
                    this,
                    Invocation.method(#getRevenueAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeForecasts: includeForecasts,
                      #includeComparisons: includeComparisons,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i15.RevenueAnalytics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i2.SystemHealth>> getSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemHealth, []),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i2.SystemHealth>>.value(
                  _FakeEither_2<_i11.Failure, _i2.SystemHealth>(
                    this,
                    Invocation.method(#getSystemHealth, []),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i2.SystemHealth>>);

  @override
  _i6.Stream<_i4.Either<_i11.Failure, _i2.SystemHealth>> watchSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemHealth, []),
            returnValue:
                _i6.Stream<_i4.Either<_i11.Failure, _i2.SystemHealth>>.empty(),
          )
          as _i6.Stream<_i4.Either<_i11.Failure, _i2.SystemHealth>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, String>> exportAnalyticsData({
    required _i2.AnalyticsExportType? type,
    required DateTime? startDate,
    required DateTime? endDate,
    required _i2.AnalyticsExportFormat? format,
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#exportAnalyticsData, [], {
              #type: type,
              #startDate: startDate,
              #endDate: endDate,
              #format: format,
              #filters: filters,
            }),
            returnValue: _i6.Future<_i4.Either<_i11.Failure, String>>.value(
              _FakeEither_2<_i11.Failure, String>(
                this,
                Invocation.method(#exportAnalyticsData, [], {
                  #type: type,
                  #startDate: startDate,
                  #endDate: endDate,
                  #format: format,
                  #filters: filters,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, String>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i2.AnalyticsSummary>>
  getAnalyticsSummary({
    required DateTime? startDate,
    required DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsSummary, [], {
              #startDate: startDate,
              #endDate: endDate,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i2.AnalyticsSummary>
                >.value(
                  _FakeEither_2<_i11.Failure, _i2.AnalyticsSummary>(
                    this,
                    Invocation.method(#getAnalyticsSummary, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i2.AnalyticsSummary>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i2.ComparativeAnalytics>>
  getComparativeAnalytics({
    required DateTime? currentStart,
    required DateTime? currentEnd,
    required DateTime? previousStart,
    required DateTime? previousEnd,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getComparativeAnalytics, [], {
              #currentStart: currentStart,
              #currentEnd: currentEnd,
              #previousStart: previousStart,
              #previousEnd: previousEnd,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i2.ComparativeAnalytics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i2.ComparativeAnalytics>(
                    this,
                    Invocation.method(#getComparativeAnalytics, [], {
                      #currentStart: currentStart,
                      #currentEnd: currentEnd,
                      #previousStart: previousStart,
                      #previousEnd: previousEnd,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i2.ComparativeAnalytics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i2.TopPerformingMetrics>>
  getTopPerformingMetrics({
    required DateTime? startDate,
    required DateTime? endDate,
    int? limit = 10,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTopPerformingMetrics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #limit: limit,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i2.TopPerformingMetrics>
                >.value(
                  _FakeEither_2<_i11.Failure, _i2.TopPerformingMetrics>(
                    this,
                    Invocation.method(#getTopPerformingMetrics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i2.TopPerformingMetrics>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, List<_i2.AnalyticsAlert>>>
  getAnalyticsAlerts({
    _i2.AnalyticsAlertSeverity? severity,
    bool? unreadOnly = false,
    int? limit = 50,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsAlerts, [], {
              #severity: severity,
              #unreadOnly: unreadOnly,
              #limit: limit,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, List<_i2.AnalyticsAlert>>
                >.value(
                  _FakeEither_2<_i11.Failure, List<_i2.AnalyticsAlert>>(
                    this,
                    Invocation.method(#getAnalyticsAlerts, [], {
                      #severity: severity,
                      #unreadOnly: unreadOnly,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, List<_i2.AnalyticsAlert>>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, void>> markAlertAsRead(String? alertId) =>
      (super.noSuchMethod(
            Invocation.method(#markAlertAsRead, [alertId]),
            returnValue: _i6.Future<_i4.Either<_i11.Failure, void>>.value(
              _FakeEither_2<_i11.Failure, void>(
                this,
                Invocation.method(#markAlertAsRead, [alertId]),
              ),
            ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, void>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, Map<String, dynamic>>>
  executeCustomQuery({
    required String? query,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeCustomQuery, [], {
              #query: query,
              #parameters: parameters,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, Map<String, dynamic>>
                >.value(
                  _FakeEither_2<_i11.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#executeCustomQuery, [], {
                      #query: query,
                      #parameters: parameters,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, Map<String, dynamic>>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i16.GeneratedReport>> generateReport({
    required _i16.ReportConfig? config,
    Map<String, dynamic>? customData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateReport, [], {
              #config: config,
              #customData: customData,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, _i16.GeneratedReport>
                >.value(
                  _FakeEither_2<_i11.Failure, _i16.GeneratedReport>(
                    this,
                    Invocation.method(#generateReport, [], {
                      #config: config,
                      #customData: customData,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i16.GeneratedReport>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, List<_i16.ReportConfig>>>
  getReportConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getReportConfigs, []),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, List<_i16.ReportConfig>>
                >.value(
                  _FakeEither_2<_i11.Failure, List<_i16.ReportConfig>>(
                    this,
                    Invocation.method(#getReportConfigs, []),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, List<_i16.ReportConfig>>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>> createReportConfig({
    required String? name,
    required String? description,
    required _i16.ReportType? type,
    required _i16.ReportFormat? format,
    required DateTime? startDate,
    required DateTime? endDate,
    required List<String>? metrics,
    required List<String>? dimensions,
    List<_i16.ReportFilter>? filters = const [],
    _i16.ReportSchedule? schedule,
    List<String>? recipients = const [],
    Map<String, dynamic>? customSettings = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createReportConfig, [], {
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>>.value(
                  _FakeEither_2<_i11.Failure, _i16.ReportConfig>(
                    this,
                    Invocation.method(#createReportConfig, [], {
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>> updateReportConfig({
    required String? id,
    String? name,
    String? description,
    _i16.ReportType? type,
    _i16.ReportFormat? format,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? metrics,
    List<String>? dimensions,
    List<_i16.ReportFilter>? filters,
    _i16.ReportSchedule? schedule,
    List<String>? recipients,
    Map<String, dynamic>? customSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateReportConfig, [], {
              #id: id,
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>>.value(
                  _FakeEither_2<_i11.Failure, _i16.ReportConfig>(
                    this,
                    Invocation.method(#updateReportConfig, [], {
                      #id: id,
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, _i16.ReportConfig>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, void>> deleteReportConfig(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteReportConfig, [id]),
            returnValue: _i6.Future<_i4.Either<_i11.Failure, void>>.value(
              _FakeEither_2<_i11.Failure, void>(
                this,
                Invocation.method(#deleteReportConfig, [id]),
              ),
            ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, void>>);

  @override
  _i6.Future<_i4.Either<_i11.Failure, List<_i16.GeneratedReport>>>
  getGeneratedReports({String? configId, int? limit = 20, int? offset = 0}) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneratedReports, [], {
              #configId: configId,
              #limit: limit,
              #offset: offset,
            }),
            returnValue:
                _i6.Future<
                  _i4.Either<_i11.Failure, List<_i16.GeneratedReport>>
                >.value(
                  _FakeEither_2<_i11.Failure, List<_i16.GeneratedReport>>(
                    this,
                    Invocation.method(#getGeneratedReports, [], {
                      #configId: configId,
                      #limit: limit,
                      #offset: offset,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i4.Either<_i11.Failure, List<_i16.GeneratedReport>>>);
}

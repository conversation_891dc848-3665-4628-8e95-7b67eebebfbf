# 🔧 Staging Environment Configuration
# This file defines the staging environment settings for GitHub Actions

name: staging
description: "Staging environment for CareNow MVP - Pre-production testing"

# Environment variables for staging
environment:
  FLUTTER_ENV: staging
  APP_VERSION: "1.0.0-staging"
  BUILD_MODE: release
  
  # Firebase Configuration (Staging)
  FIREBASE_PROJECT_ID: "carenow-staging"
  FIREBASE_WEB_API_KEY: ${{ secrets.FIREBASE_WEB_API_KEY_STAGING }}
  FIREBASE_ANDROID_API_KEY: ${{ secrets.FIREBASE_ANDROID_API_KEY_STAGING }}
  FIREBASE_IOS_API_KEY: ${{ secrets.FIREBASE_IOS_API_KEY_STAGING }}
  
  # Security Configuration
  SECURITY_LEVEL: "MEDIUM"
  ENCRYPTION_ENABLED: "true"
  SESSION_TIMEOUT: "1800"  # 30 minutes
  RATE_LIMITING_ENABLED: "true"
  MAX_REQUESTS_PER_MINUTE: "200"
  
  # Performance Configuration
  PERFORMANCE_MONITORING: "true"
  CRASH_REPORTING: "true"
  ANALYTICS_ENABLED: "true"
  CACHING_ENABLED: "true"
  
  # API Configuration
  API_BASE_URL: "https://api-staging.carenow.com"
  API_TIMEOUT: "30"
  API_MAX_RETRIES: "3"
  
  # Build Configuration
  TREE_SHAKE_ICONS: "true"
  OBFUSCATION: "false"
  SOURCE_MAPS: "true"
  
# Deployment settings
deployment:
  auto_deploy: true
  require_approval: false
  
  # Web deployment
  web:
    hosting_target: "staging"
    custom_domain: "staging.carenow.com"
    
  # Android deployment
  android:
    signing_required: false
    distribution: "internal"
    
  # iOS deployment
  ios:
    signing_required: false
    distribution: "internal"

# Testing configuration
testing:
  run_unit_tests: true
  run_integration_tests: true
  run_security_tests: true
  run_performance_tests: true
  coverage_threshold: 80
  
# Monitoring and alerts
monitoring:
  error_reporting: true
  performance_monitoring: true
  uptime_monitoring: true
  alert_channels:
    - slack
    - email
  
# Security settings
security:
  vulnerability_scanning: true
  dependency_scanning: true
  secret_scanning: true
  code_scanning: true
  compliance_checks: true
  
# Load testing
load_testing:
  enabled: true
  concurrent_users: 50
  duration: "5m"
  
# Backup and recovery
backup:
  enabled: true
  frequency: "daily"
  retention: "7d"

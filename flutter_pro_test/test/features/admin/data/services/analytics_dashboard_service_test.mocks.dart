// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/admin/data/services/analytics_dashboard_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:typed_data' as _i10;

import 'package:cloud_firestore/cloud_firestore.dart' as _i4;
import 'package:cloud_firestore_platform_interface/cloud_firestore_platform_interface.dart'
    as _i3;
import 'package:firebase_analytics/firebase_analytics.dart' as _i6;
import 'package:firebase_analytics_platform_interface/firebase_analytics_platform_interface.dart'
    as _i8;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:firebase_performance/firebase_performance.dart' as _i7;
import 'package:flutter_pro_test/core/analytics/business_analytics_service.dart'
    as _i12;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i11;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i13;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSettings_1 extends _i1.SmartFake implements _i3.Settings {
  _FakeSettings_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCollectionReference_2<T extends Object?> extends _i1.SmartFake
    implements _i4.CollectionReference<T> {
  _FakeCollectionReference_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWriteBatch_3 extends _i1.SmartFake implements _i4.WriteBatch {
  _FakeWriteBatch_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLoadBundleTask_4 extends _i1.SmartFake
    implements _i4.LoadBundleTask {
  _FakeLoadBundleTask_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeQuerySnapshot_5<T1 extends Object?> extends _i1.SmartFake
    implements _i4.QuerySnapshot<T1> {
  _FakeQuerySnapshot_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeQuery_6<T extends Object?> extends _i1.SmartFake
    implements _i4.Query<T> {
  _FakeQuery_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDocumentReference_7<T extends Object?> extends _i1.SmartFake
    implements _i4.DocumentReference<T> {
  _FakeDocumentReference_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_8<T1> extends _i1.SmartFake implements _i5.Future<T1> {
  _FakeFuture_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAnalytics_9 extends _i1.SmartFake
    implements _i6.FirebaseAnalytics {
  _FakeFirebaseAnalytics_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_10 extends _i1.SmartFake
    implements _i7.FirebasePerformance {
  _FakeFirebasePerformance_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseFirestore_11 extends _i1.SmartFake
    implements _i4.FirebaseFirestore {
  _FakeFirebaseFirestore_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDocumentSnapshot_12<T1 extends Object?> extends _i1.SmartFake
    implements _i4.DocumentSnapshot<T1> {
  _FakeDocumentSnapshot_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSnapshotMetadata_13 extends _i1.SmartFake
    implements _i4.SnapshotMetadata {
  _FakeSnapshotMetadata_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAggregateQuery_14 extends _i1.SmartFake
    implements _i4.AggregateQuery {
  _FakeAggregateQuery_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirebaseAnalytics].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalytics extends _i1.Mock implements _i6.FirebaseAnalytics {
  MockFirebaseAnalytics() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_0(this, Invocation.getter(#app)),
          )
          as _i2.FirebaseApp);

  @override
  _i5.Future<String?> get appInstanceId =>
      (super.noSuchMethod(
            Invocation.getter(#appInstanceId),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i5.Future<bool> isSupported() =>
      (super.noSuchMethod(
            Invocation.method(#isSupported, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<int?> getSessionId() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionId, []),
            returnValue: _i5.Future<int?>.value(),
          )
          as _i5.Future<int?>);

  @override
  _i5.Future<void> logEvent({
    required String? name,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logEvent, [], {
              #name: name,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setConsent({
    bool? adStorageConsentGranted,
    bool? analyticsStorageConsentGranted,
    bool? adPersonalizationSignalsConsentGranted,
    bool? adUserDataConsentGranted,
    bool? functionalityStorageConsentGranted,
    bool? personalizationStorageConsentGranted,
    bool? securityStorageConsentGranted,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setConsent, [], {
              #adStorageConsentGranted: adStorageConsentGranted,
              #analyticsStorageConsentGranted: analyticsStorageConsentGranted,
              #adPersonalizationSignalsConsentGranted:
                  adPersonalizationSignalsConsentGranted,
              #adUserDataConsentGranted: adUserDataConsentGranted,
              #functionalityStorageConsentGranted:
                  functionalityStorageConsentGranted,
              #personalizationStorageConsentGranted:
                  personalizationStorageConsentGranted,
              #securityStorageConsentGranted: securityStorageConsentGranted,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setDefaultEventParameters(
    Map<String, Object?>? defaultParameters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setDefaultEventParameters, [defaultParameters]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setAnalyticsCollectionEnabled(bool? enabled) =>
      (super.noSuchMethod(
            Invocation.method(#setAnalyticsCollectionEnabled, [enabled]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserId({
    String? id,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [], {
              #id: id,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setCurrentScreen({
    required String? screenName,
    String? screenClassOverride = 'Flutter',
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setCurrentScreen, [], {
              #screenName: screenName,
              #screenClassOverride: screenClassOverride,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserProperty({
    required String? name,
    required String? value,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setUserProperty, [], {
              #name: name,
              #value: value,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> resetAnalyticsData() =>
      (super.noSuchMethod(
            Invocation.method(#resetAnalyticsData, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAddPaymentInfo({
    String? coupon,
    String? currency,
    String? paymentType,
    double? value,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAddPaymentInfo, [], {
              #coupon: coupon,
              #currency: currency,
              #paymentType: paymentType,
              #value: value,
              #items: items,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAddShippingInfo({
    String? coupon,
    String? currency,
    double? value,
    String? shippingTier,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAddShippingInfo, [], {
              #coupon: coupon,
              #currency: currency,
              #value: value,
              #shippingTier: shippingTier,
              #items: items,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAddToCart({
    List<_i8.AnalyticsEventItem>? items,
    double? value,
    String? currency,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAddToCart, [], {
              #items: items,
              #value: value,
              #currency: currency,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAddToWishlist({
    List<_i8.AnalyticsEventItem>? items,
    double? value,
    String? currency,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAddToWishlist, [], {
              #items: items,
              #value: value,
              #currency: currency,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAdImpression({
    String? adPlatform,
    String? adSource,
    String? adFormat,
    String? adUnitName,
    double? value,
    String? currency,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAdImpression, [], {
              #adPlatform: adPlatform,
              #adSource: adSource,
              #adFormat: adFormat,
              #adUnitName: adUnitName,
              #value: value,
              #currency: currency,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logAppOpen({
    _i8.AnalyticsCallOptions? callOptions,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAppOpen, [], {
              #callOptions: callOptions,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logBeginCheckout({
    double? value,
    String? currency,
    List<_i8.AnalyticsEventItem>? items,
    String? coupon,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logBeginCheckout, [], {
              #value: value,
              #currency: currency,
              #items: items,
              #coupon: coupon,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logCampaignDetails({
    required String? source,
    required String? medium,
    required String? campaign,
    String? term,
    String? content,
    String? aclid,
    String? cp1,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logCampaignDetails, [], {
              #source: source,
              #medium: medium,
              #campaign: campaign,
              #term: term,
              #content: content,
              #aclid: aclid,
              #cp1: cp1,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logEarnVirtualCurrency({
    required String? virtualCurrencyName,
    required num? value,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logEarnVirtualCurrency, [], {
              #virtualCurrencyName: virtualCurrencyName,
              #value: value,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logGenerateLead({
    String? currency,
    double? value,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logGenerateLead, [], {
              #currency: currency,
              #value: value,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logJoinGroup({
    required String? groupId,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logJoinGroup, [], {
              #groupId: groupId,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logLevelUp({
    required int? level,
    String? character,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logLevelUp, [], {
              #level: level,
              #character: character,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logLevelStart({
    required String? levelName,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logLevelStart, [], {
              #levelName: levelName,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logLevelEnd({
    required String? levelName,
    int? success,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logLevelEnd, [], {
              #levelName: levelName,
              #success: success,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSetCheckoutOption({
    required int? checkoutStep,
    required String? checkoutOption,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSetCheckoutOption, [], {
              #checkoutStep: checkoutStep,
              #checkoutOption: checkoutOption,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logLogin({
    String? loginMethod,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logLogin, [], {
              #loginMethod: loginMethod,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logPostScore({
    required int? score,
    int? level,
    String? character,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logPostScore, [], {
              #score: score,
              #level: level,
              #character: character,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logPurchase({
    String? currency,
    String? coupon,
    double? value,
    List<_i8.AnalyticsEventItem>? items,
    double? tax,
    double? shipping,
    String? transactionId,
    String? affiliation,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logPurchase, [], {
              #currency: currency,
              #coupon: coupon,
              #value: value,
              #items: items,
              #tax: tax,
              #shipping: shipping,
              #transactionId: transactionId,
              #affiliation: affiliation,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logRemoveFromCart({
    String? currency,
    double? value,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logRemoveFromCart, [], {
              #currency: currency,
              #value: value,
              #items: items,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logScreenView({
    String? screenClass,
    String? screenName,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenClass: screenClass,
              #screenName: screenName,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSelectItem({
    String? itemListId,
    String? itemListName,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSelectItem, [], {
              #itemListId: itemListId,
              #itemListName: itemListName,
              #items: items,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSelectPromotion({
    String? creativeName,
    String? creativeSlot,
    List<_i8.AnalyticsEventItem>? items,
    String? locationId,
    String? promotionId,
    String? promotionName,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSelectPromotion, [], {
              #creativeName: creativeName,
              #creativeSlot: creativeSlot,
              #items: items,
              #locationId: locationId,
              #promotionId: promotionId,
              #promotionName: promotionName,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logViewCart({
    String? currency,
    double? value,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logViewCart, [], {
              #currency: currency,
              #value: value,
              #items: items,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSearch({
    required String? searchTerm,
    int? numberOfNights,
    int? numberOfRooms,
    int? numberOfPassengers,
    String? origin,
    String? destination,
    String? startDate,
    String? endDate,
    String? travelClass,
    Map<String, Object>? parameters,
    _i8.AnalyticsCallOptions? callOptions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSearch, [], {
              #searchTerm: searchTerm,
              #numberOfNights: numberOfNights,
              #numberOfRooms: numberOfRooms,
              #numberOfPassengers: numberOfPassengers,
              #origin: origin,
              #destination: destination,
              #startDate: startDate,
              #endDate: endDate,
              #travelClass: travelClass,
              #parameters: parameters,
              #callOptions: callOptions,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSelectContent({
    required String? contentType,
    required String? itemId,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSelectContent, [], {
              #contentType: contentType,
              #itemId: itemId,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logShare({
    required String? contentType,
    required String? itemId,
    required String? method,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logShare, [], {
              #contentType: contentType,
              #itemId: itemId,
              #method: method,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSignUp({
    required String? signUpMethod,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSignUp, [], {
              #signUpMethod: signUpMethod,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logSpendVirtualCurrency({
    required String? itemName,
    required String? virtualCurrencyName,
    required num? value,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logSpendVirtualCurrency, [], {
              #itemName: itemName,
              #virtualCurrencyName: virtualCurrencyName,
              #value: value,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logTutorialBegin({Map<String, Object>? parameters}) =>
      (super.noSuchMethod(
            Invocation.method(#logTutorialBegin, [], {#parameters: parameters}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logTutorialComplete({Map<String, Object>? parameters}) =>
      (super.noSuchMethod(
            Invocation.method(#logTutorialComplete, [], {
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logUnlockAchievement({
    required String? id,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logUnlockAchievement, [], {
              #id: id,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logViewItem({
    String? currency,
    double? value,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logViewItem, [], {
              #currency: currency,
              #value: value,
              #items: items,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logViewItemList({
    List<_i8.AnalyticsEventItem>? items,
    String? itemListId,
    String? itemListName,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logViewItemList, [], {
              #items: items,
              #itemListId: itemListId,
              #itemListName: itemListName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logViewPromotion({
    String? creativeName,
    String? creativeSlot,
    List<_i8.AnalyticsEventItem>? items,
    String? locationId,
    String? promotionId,
    String? promotionName,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logViewPromotion, [], {
              #creativeName: creativeName,
              #creativeSlot: creativeSlot,
              #items: items,
              #locationId: locationId,
              #promotionId: promotionId,
              #promotionName: promotionName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logViewSearchResults({
    required String? searchTerm,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logViewSearchResults, [], {
              #searchTerm: searchTerm,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logRefund({
    String? currency,
    String? coupon,
    double? value,
    double? tax,
    double? shipping,
    String? transactionId,
    String? affiliation,
    List<_i8.AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logRefund, [], {
              #currency: currency,
              #coupon: coupon,
              #value: value,
              #tax: tax,
              #shipping: shipping,
              #transactionId: transactionId,
              #affiliation: affiliation,
              #items: items,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setSessionTimeoutDuration(Duration? timeout) =>
      (super.noSuchMethod(
            Invocation.method(#setSessionTimeoutDuration, [timeout]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initiateOnDeviceConversionMeasurementWithEmailAddress(
    String? emailAddress,
  ) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateOnDeviceConversionMeasurementWithEmailAddress,
              [emailAddress],
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initiateOnDeviceConversionMeasurementWithPhoneNumber(
    String? phoneNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateOnDeviceConversionMeasurementWithPhoneNumber,
              [phoneNumber],
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initiateOnDeviceConversionMeasurementWithHashedEmailAddress(
    String? hashedEmailAddress,
  ) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateOnDeviceConversionMeasurementWithHashedEmailAddress,
              [hashedEmailAddress],
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> initiateOnDeviceConversionMeasurementWithHashedPhoneNumber(
    String? hashedPhoneNumber,
  ) =>
      (super.noSuchMethod(
            Invocation.method(
              #initiateOnDeviceConversionMeasurementWithHashedPhoneNumber,
              [hashedPhoneNumber],
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [FirebaseFirestore].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseFirestore extends _i1.Mock implements _i4.FirebaseFirestore {
  MockFirebaseFirestore() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_0(this, Invocation.getter(#app)),
          )
          as _i2.FirebaseApp);

  @override
  String get databaseURL =>
      (super.noSuchMethod(
            Invocation.getter(#databaseURL),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#databaseURL),
            ),
          )
          as String);

  @override
  String get databaseId =>
      (super.noSuchMethod(
            Invocation.getter(#databaseId),
            returnValue: _i9.dummyValue<String>(
              this,
              Invocation.getter(#databaseId),
            ),
          )
          as String);

  @override
  _i3.Settings get settings =>
      (super.noSuchMethod(
            Invocation.getter(#settings),
            returnValue: _FakeSettings_1(this, Invocation.getter(#settings)),
          )
          as _i3.Settings);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set databaseURL(String? _databaseURL) => super.noSuchMethod(
    Invocation.setter(#databaseURL, _databaseURL),
    returnValueForMissingStub: null,
  );

  @override
  set databaseId(String? _databaseId) => super.noSuchMethod(
    Invocation.setter(#databaseId, _databaseId),
    returnValueForMissingStub: null,
  );

  @override
  set settings(_i3.Settings? settings) => super.noSuchMethod(
    Invocation.setter(#settings, settings),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i4.CollectionReference<Map<String, dynamic>> collection(
    String? collectionPath,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#collection, [collectionPath]),
            returnValue: _FakeCollectionReference_2<Map<String, dynamic>>(
              this,
              Invocation.method(#collection, [collectionPath]),
            ),
          )
          as _i4.CollectionReference<Map<String, dynamic>>);

  @override
  _i4.WriteBatch batch() =>
      (super.noSuchMethod(
            Invocation.method(#batch, []),
            returnValue: _FakeWriteBatch_3(this, Invocation.method(#batch, [])),
          )
          as _i4.WriteBatch);

  @override
  _i5.Future<void> clearPersistence() =>
      (super.noSuchMethod(
            Invocation.method(#clearPersistence, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> enablePersistence([
    _i3.PersistenceSettings? persistenceSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#enablePersistence, [persistenceSettings]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i4.LoadBundleTask loadBundle(_i10.Uint8List? bundle) =>
      (super.noSuchMethod(
            Invocation.method(#loadBundle, [bundle]),
            returnValue: _FakeLoadBundleTask_4(
              this,
              Invocation.method(#loadBundle, [bundle]),
            ),
          )
          as _i4.LoadBundleTask);

  @override
  void useFirestoreEmulator(
    String? host,
    int? port, {
    bool? sslEnabled = false,
    bool? automaticHostMapping = true,
  }) => super.noSuchMethod(
    Invocation.method(
      #useFirestoreEmulator,
      [host, port],
      {#sslEnabled: sslEnabled, #automaticHostMapping: automaticHostMapping},
    ),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<_i4.QuerySnapshot<T>> namedQueryWithConverterGet<T>(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
    required _i4.FromFirestore<T>? fromFirestore,
    required _i4.ToFirestore<T>? toFirestore,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #namedQueryWithConverterGet,
              [name],
              {
                #options: options,
                #fromFirestore: fromFirestore,
                #toFirestore: toFirestore,
              },
            ),
            returnValue: _i5.Future<_i4.QuerySnapshot<T>>.value(
              _FakeQuerySnapshot_5<T>(
                this,
                Invocation.method(
                  #namedQueryWithConverterGet,
                  [name],
                  {
                    #options: options,
                    #fromFirestore: fromFirestore,
                    #toFirestore: toFirestore,
                  },
                ),
              ),
            ),
          )
          as _i5.Future<_i4.QuerySnapshot<T>>);

  @override
  _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>> namedQueryGet(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#namedQueryGet, [name], {#options: options}),
            returnValue:
                _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>>.value(
                  _FakeQuerySnapshot_5<Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #namedQueryGet,
                      [name],
                      {#options: options},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>>);

  @override
  _i4.Query<Map<String, dynamic>> collectionGroup(String? collectionPath) =>
      (super.noSuchMethod(
            Invocation.method(#collectionGroup, [collectionPath]),
            returnValue: _FakeQuery_6<Map<String, dynamic>>(
              this,
              Invocation.method(#collectionGroup, [collectionPath]),
            ),
          )
          as _i4.Query<Map<String, dynamic>>);

  @override
  _i5.Future<void> disableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#disableNetwork, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i4.DocumentReference<Map<String, dynamic>> doc(String? documentPath) =>
      (super.noSuchMethod(
            Invocation.method(#doc, [documentPath]),
            returnValue: _FakeDocumentReference_7<Map<String, dynamic>>(
              this,
              Invocation.method(#doc, [documentPath]),
            ),
          )
          as _i4.DocumentReference<Map<String, dynamic>>);

  @override
  _i5.Future<void> enableNetwork() =>
      (super.noSuchMethod(
            Invocation.method(#enableNetwork, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Stream<void> snapshotsInSync() =>
      (super.noSuchMethod(
            Invocation.method(#snapshotsInSync, []),
            returnValue: _i5.Stream<void>.empty(),
          )
          as _i5.Stream<void>);

  @override
  _i5.Future<T> runTransaction<T>(
    _i4.TransactionHandler<T>? transactionHandler, {
    Duration? timeout = const Duration(seconds: 30),
    int? maxAttempts = 5,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #runTransaction,
              [transactionHandler],
              {#timeout: timeout, #maxAttempts: maxAttempts},
            ),
            returnValue:
                _i9.ifNotNull(
                  _i9.dummyValueOrNull<T>(
                    this,
                    Invocation.method(
                      #runTransaction,
                      [transactionHandler],
                      {#timeout: timeout, #maxAttempts: maxAttempts},
                    ),
                  ),
                  (T v) => _i5.Future<T>.value(v),
                ) ??
                _FakeFuture_8<T>(
                  this,
                  Invocation.method(
                    #runTransaction,
                    [transactionHandler],
                    {#timeout: timeout, #maxAttempts: maxAttempts},
                  ),
                ),
          )
          as _i5.Future<T>);

  @override
  _i5.Future<void> terminate() =>
      (super.noSuchMethod(
            Invocation.method(#terminate, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> waitForPendingWrites() =>
      (super.noSuchMethod(
            Invocation.method(#waitForPendingWrites, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setIndexConfiguration({
    required List<_i3.Index>? indexes,
    List<_i3.FieldOverrides>? fieldOverrides,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setIndexConfiguration, [], {
              #indexes: indexes,
              #fieldOverrides: fieldOverrides,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setIndexConfigurationFromJSON(String? json) =>
      (super.noSuchMethod(
            Invocation.method(#setIndexConfigurationFromJSON, [json]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i11.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_9(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i6.FirebaseAnalytics);

  @override
  _i7.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_10(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i7.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i7.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i5.Future<_i7.Trace?>.value(),
          )
          as _i5.Future<_i7.Trace?>);

  @override
  _i5.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i7.HttpMetric?> startHttpMetric(
    String? url,
    _i7.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i5.Future<_i7.HttpMetric?>.value(),
          )
          as _i5.Future<_i7.HttpMetric?>);

  @override
  _i5.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [BusinessAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBusinessAnalyticsService extends _i1.Mock
    implements _i12.BusinessAnalyticsService {
  MockBusinessAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initialize({
    required _i11.FirebaseAnalyticsService? analyticsService,
    required _i13.MonitoringService? monitoringService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUser({
    required String? userId,
    required String? userType,
    Map<String, String>? userProperties,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setUser, [], {
              #userId: userId,
              #userType: userType,
              #userProperties: userProperties,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackUserAction({
    required String? actionName,
    String? category,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #category: category,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackFunnelStage({
    required String? funnelName,
    required String? stageName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackFunnelStage, [], {
              #funnelName: funnelName,
              #stageName: stageName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackBusinessEvent({
    required String? eventName,
    double? revenue,
    String? currency,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackBusinessEvent, [], {
              #eventName: eventName,
              #revenue: revenue,
              #currency: currency,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackEngagement({
    required String? engagementType,
    Duration? duration,
    int? count,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackEngagement, [], {
              #engagementType: engagementType,
              #duration: duration,
              #count: count,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    String? screenName,
    String? userAction,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #screenName: screenName,
              #userAction: userAction,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  List<String> getUserJourney() =>
      (super.noSuchMethod(
            Invocation.method(#getUserJourney, []),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  Map<String, int> getFeatureUsageStats() =>
      (super.noSuchMethod(
            Invocation.method(#getFeatureUsageStats, []),
            returnValue: <String, int>{},
          )
          as Map<String, int>);

  @override
  Map<String, dynamic> getSessionInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionInfo, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i13.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> initialize({
    _i11.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i13.LogEntry> getRecentLogs({
    int? limit = 100,
    _i13.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i13.LogEntry>[],
          )
          as List<_i13.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [DocumentReference].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockDocumentReference<T extends Object?> extends _i1.Mock
    implements _i4.DocumentReference<T> {
  MockDocumentReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.FirebaseFirestore get firestore =>
      (super.noSuchMethod(
            Invocation.getter(#firestore),
            returnValue: _FakeFirebaseFirestore_11(
              this,
              Invocation.getter(#firestore),
            ),
          )
          as _i4.FirebaseFirestore);

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  _i4.CollectionReference<T> get parent =>
      (super.noSuchMethod(
            Invocation.getter(#parent),
            returnValue: _FakeCollectionReference_2<T>(
              this,
              Invocation.getter(#parent),
            ),
          )
          as _i4.CollectionReference<T>);

  @override
  String get path =>
      (super.noSuchMethod(
            Invocation.getter(#path),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#path)),
          )
          as String);

  @override
  _i4.CollectionReference<Map<String, dynamic>> collection(
    String? collectionPath,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#collection, [collectionPath]),
            returnValue: _FakeCollectionReference_2<Map<String, dynamic>>(
              this,
              Invocation.method(#collection, [collectionPath]),
            ),
          )
          as _i4.CollectionReference<Map<String, dynamic>>);

  @override
  _i5.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> update(Map<Object, Object?>? data) =>
      (super.noSuchMethod(
            Invocation.method(#update, [data]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i4.DocumentSnapshot<T>> get([_i3.GetOptions? options]) =>
      (super.noSuchMethod(
            Invocation.method(#get, [options]),
            returnValue: _i5.Future<_i4.DocumentSnapshot<T>>.value(
              _FakeDocumentSnapshot_12<T>(
                this,
                Invocation.method(#get, [options]),
              ),
            ),
          )
          as _i5.Future<_i4.DocumentSnapshot<T>>);

  @override
  _i5.Stream<_i4.DocumentSnapshot<T>> snapshots({
    bool? includeMetadataChanges = false,
    _i3.ListenSource? source = _i3.ListenSource.defaultSource,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#snapshots, [], {
              #includeMetadataChanges: includeMetadataChanges,
              #source: source,
            }),
            returnValue: _i5.Stream<_i4.DocumentSnapshot<T>>.empty(),
          )
          as _i5.Stream<_i4.DocumentSnapshot<T>>);

  @override
  _i5.Future<void> set(T? data, [_i3.SetOptions? options]) =>
      (super.noSuchMethod(
            Invocation.method(#set, [data, options]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i4.DocumentReference<R> withConverter<R>({
    required _i4.FromFirestore<R>? fromFirestore,
    required _i4.ToFirestore<R>? toFirestore,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#withConverter, [], {
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            }),
            returnValue: _FakeDocumentReference_7<R>(
              this,
              Invocation.method(#withConverter, [], {
                #fromFirestore: fromFirestore,
                #toFirestore: toFirestore,
              }),
            ),
          )
          as _i4.DocumentReference<R>);
}

/// A class which mocks [DocumentSnapshot].
///
/// See the documentation for Mockito's code generation for more information.
class MockDocumentSnapshot<T extends Object?> extends _i1.Mock
    implements _i4.DocumentSnapshot<T> {
  MockDocumentSnapshot() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  _i4.DocumentReference<T> get reference =>
      (super.noSuchMethod(
            Invocation.getter(#reference),
            returnValue: _FakeDocumentReference_7<T>(
              this,
              Invocation.getter(#reference),
            ),
          )
          as _i4.DocumentReference<T>);

  @override
  _i4.SnapshotMetadata get metadata =>
      (super.noSuchMethod(
            Invocation.getter(#metadata),
            returnValue: _FakeSnapshotMetadata_13(
              this,
              Invocation.getter(#metadata),
            ),
          )
          as _i4.SnapshotMetadata);

  @override
  bool get exists =>
      (super.noSuchMethod(Invocation.getter(#exists), returnValue: false)
          as bool);

  @override
  dynamic get(Object? field) =>
      super.noSuchMethod(Invocation.method(#get, [field]));

  @override
  dynamic operator [](Object? field) =>
      super.noSuchMethod(Invocation.method(#[], [field]));
}

/// A class which mocks [CollectionReference].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockCollectionReference<T extends Object?> extends _i1.Mock
    implements _i4.CollectionReference<T> {
  MockCollectionReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id =>
      (super.noSuchMethod(
            Invocation.getter(#id),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#id)),
          )
          as String);

  @override
  String get path =>
      (super.noSuchMethod(
            Invocation.getter(#path),
            returnValue: _i9.dummyValue<String>(this, Invocation.getter(#path)),
          )
          as String);

  @override
  _i4.FirebaseFirestore get firestore =>
      (super.noSuchMethod(
            Invocation.getter(#firestore),
            returnValue: _FakeFirebaseFirestore_11(
              this,
              Invocation.getter(#firestore),
            ),
          )
          as _i4.FirebaseFirestore);

  @override
  Map<String, dynamic> get parameters =>
      (super.noSuchMethod(
            Invocation.getter(#parameters),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<_i4.DocumentReference<T>> add(T? data) =>
      (super.noSuchMethod(
            Invocation.method(#add, [data]),
            returnValue: _i5.Future<_i4.DocumentReference<T>>.value(
              _FakeDocumentReference_7<T>(
                this,
                Invocation.method(#add, [data]),
              ),
            ),
          )
          as _i5.Future<_i4.DocumentReference<T>>);

  @override
  _i4.DocumentReference<T> doc([String? path]) =>
      (super.noSuchMethod(
            Invocation.method(#doc, [path]),
            returnValue: _FakeDocumentReference_7<T>(
              this,
              Invocation.method(#doc, [path]),
            ),
          )
          as _i4.DocumentReference<T>);

  @override
  _i4.CollectionReference<R> withConverter<R extends Object?>({
    required _i4.FromFirestore<R>? fromFirestore,
    required _i4.ToFirestore<R>? toFirestore,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#withConverter, [], {
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            }),
            returnValue: _FakeCollectionReference_2<R>(
              this,
              Invocation.method(#withConverter, [], {
                #fromFirestore: fromFirestore,
                #toFirestore: toFirestore,
              }),
            ),
          )
          as _i4.CollectionReference<R>);

  @override
  _i4.Query<T> endAtDocument(_i4.DocumentSnapshot<Object?>? documentSnapshot) =>
      (super.noSuchMethod(
            Invocation.method(#endAtDocument, [documentSnapshot]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#endAtDocument, [documentSnapshot]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> endAt(Iterable<Object?>? values) =>
      (super.noSuchMethod(
            Invocation.method(#endAt, [values]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#endAt, [values]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> endBeforeDocument(
    _i4.DocumentSnapshot<Object?>? documentSnapshot,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#endBeforeDocument, [documentSnapshot]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#endBeforeDocument, [documentSnapshot]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> endBefore(Iterable<Object?>? values) =>
      (super.noSuchMethod(
            Invocation.method(#endBefore, [values]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#endBefore, [values]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i5.Future<_i4.QuerySnapshot<T>> get([_i3.GetOptions? options]) =>
      (super.noSuchMethod(
            Invocation.method(#get, [options]),
            returnValue: _i5.Future<_i4.QuerySnapshot<T>>.value(
              _FakeQuerySnapshot_5<T>(this, Invocation.method(#get, [options])),
            ),
          )
          as _i5.Future<_i4.QuerySnapshot<T>>);

  @override
  _i4.Query<T> limit(int? limit) =>
      (super.noSuchMethod(
            Invocation.method(#limit, [limit]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#limit, [limit]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> limitToLast(int? limit) =>
      (super.noSuchMethod(
            Invocation.method(#limitToLast, [limit]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#limitToLast, [limit]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i5.Stream<_i4.QuerySnapshot<T>> snapshots({
    bool? includeMetadataChanges = false,
    _i3.ListenSource? source = _i3.ListenSource.defaultSource,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#snapshots, [], {
              #includeMetadataChanges: includeMetadataChanges,
              #source: source,
            }),
            returnValue: _i5.Stream<_i4.QuerySnapshot<T>>.empty(),
          )
          as _i5.Stream<_i4.QuerySnapshot<T>>);

  @override
  _i4.Query<T> orderBy(Object? field, {bool? descending = false}) =>
      (super.noSuchMethod(
            Invocation.method(#orderBy, [field], {#descending: descending}),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#orderBy, [field], {#descending: descending}),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> startAfterDocument(
    _i4.DocumentSnapshot<Object?>? documentSnapshot,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startAfterDocument, [documentSnapshot]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#startAfterDocument, [documentSnapshot]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> startAfter(Iterable<Object?>? values) =>
      (super.noSuchMethod(
            Invocation.method(#startAfter, [values]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#startAfter, [values]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> startAtDocument(
    _i4.DocumentSnapshot<Object?>? documentSnapshot,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startAtDocument, [documentSnapshot]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#startAtDocument, [documentSnapshot]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> startAt(Iterable<Object?>? values) =>
      (super.noSuchMethod(
            Invocation.method(#startAt, [values]),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(#startAt, [values]),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.Query<T> where(
    Object? field, {
    Object? isEqualTo,
    Object? isNotEqualTo,
    Object? isLessThan,
    Object? isLessThanOrEqualTo,
    Object? isGreaterThan,
    Object? isGreaterThanOrEqualTo,
    Object? arrayContains,
    Iterable<Object?>? arrayContainsAny,
    Iterable<Object?>? whereIn,
    Iterable<Object?>? whereNotIn,
    bool? isNull,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #where,
              [field],
              {
                #isEqualTo: isEqualTo,
                #isNotEqualTo: isNotEqualTo,
                #isLessThan: isLessThan,
                #isLessThanOrEqualTo: isLessThanOrEqualTo,
                #isGreaterThan: isGreaterThan,
                #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                #arrayContains: arrayContains,
                #arrayContainsAny: arrayContainsAny,
                #whereIn: whereIn,
                #whereNotIn: whereNotIn,
                #isNull: isNull,
              },
            ),
            returnValue: _FakeQuery_6<T>(
              this,
              Invocation.method(
                #where,
                [field],
                {
                  #isEqualTo: isEqualTo,
                  #isNotEqualTo: isNotEqualTo,
                  #isLessThan: isLessThan,
                  #isLessThanOrEqualTo: isLessThanOrEqualTo,
                  #isGreaterThan: isGreaterThan,
                  #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                  #arrayContains: arrayContains,
                  #arrayContainsAny: arrayContainsAny,
                  #whereIn: whereIn,
                  #whereNotIn: whereNotIn,
                  #isNull: isNull,
                },
              ),
            ),
          )
          as _i4.Query<T>);

  @override
  _i4.AggregateQuery count() =>
      (super.noSuchMethod(
            Invocation.method(#count, []),
            returnValue: _FakeAggregateQuery_14(
              this,
              Invocation.method(#count, []),
            ),
          )
          as _i4.AggregateQuery);

  @override
  _i4.AggregateQuery aggregate(
    _i3.AggregateField? aggregateField1, [
    _i3.AggregateField? aggregateField2,
    _i3.AggregateField? aggregateField3,
    _i3.AggregateField? aggregateField4,
    _i3.AggregateField? aggregateField5,
    _i3.AggregateField? aggregateField6,
    _i3.AggregateField? aggregateField7,
    _i3.AggregateField? aggregateField8,
    _i3.AggregateField? aggregateField9,
    _i3.AggregateField? aggregateField10,
    _i3.AggregateField? aggregateField11,
    _i3.AggregateField? aggregateField12,
    _i3.AggregateField? aggregateField13,
    _i3.AggregateField? aggregateField14,
    _i3.AggregateField? aggregateField15,
    _i3.AggregateField? aggregateField16,
    _i3.AggregateField? aggregateField17,
    _i3.AggregateField? aggregateField18,
    _i3.AggregateField? aggregateField19,
    _i3.AggregateField? aggregateField20,
    _i3.AggregateField? aggregateField21,
    _i3.AggregateField? aggregateField22,
    _i3.AggregateField? aggregateField23,
    _i3.AggregateField? aggregateField24,
    _i3.AggregateField? aggregateField25,
    _i3.AggregateField? aggregateField26,
    _i3.AggregateField? aggregateField27,
    _i3.AggregateField? aggregateField28,
    _i3.AggregateField? aggregateField29,
    _i3.AggregateField? aggregateField30,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#aggregate, [
              aggregateField1,
              aggregateField2,
              aggregateField3,
              aggregateField4,
              aggregateField5,
              aggregateField6,
              aggregateField7,
              aggregateField8,
              aggregateField9,
              aggregateField10,
              aggregateField11,
              aggregateField12,
              aggregateField13,
              aggregateField14,
              aggregateField15,
              aggregateField16,
              aggregateField17,
              aggregateField18,
              aggregateField19,
              aggregateField20,
              aggregateField21,
              aggregateField22,
              aggregateField23,
              aggregateField24,
              aggregateField25,
              aggregateField26,
              aggregateField27,
              aggregateField28,
              aggregateField29,
              aggregateField30,
            ]),
            returnValue: _FakeAggregateQuery_14(
              this,
              Invocation.method(#aggregate, [
                aggregateField1,
                aggregateField2,
                aggregateField3,
                aggregateField4,
                aggregateField5,
                aggregateField6,
                aggregateField7,
                aggregateField8,
                aggregateField9,
                aggregateField10,
                aggregateField11,
                aggregateField12,
                aggregateField13,
                aggregateField14,
                aggregateField15,
                aggregateField16,
                aggregateField17,
                aggregateField18,
                aggregateField19,
                aggregateField20,
                aggregateField21,
                aggregateField22,
                aggregateField23,
                aggregateField24,
                aggregateField25,
                aggregateField26,
                aggregateField27,
                aggregateField28,
                aggregateField29,
                aggregateField30,
              ]),
            ),
          )
          as _i4.AggregateQuery);
}

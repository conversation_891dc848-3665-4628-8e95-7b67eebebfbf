#!/bin/bash

# CareNow MVP - iOS Device Testing Execution Script
# This script orchestrates comprehensive iOS device testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🍎 CareNow MVP - iOS Device Testing Suite${NC}"
echo -e "${BLUE}===========================================${NC}"

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test_results/ios_devices"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
TEST_SESSION_DIR="$TEST_RESULTS_DIR/$TIMESTAMP"

# Create test results directory
mkdir -p "$TEST_SESSION_DIR"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking iOS testing prerequisites..."
    
    # Check if running on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "iOS testing requires macOS"
        exit 1
    fi
    
    # Check Flutter installation
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check Xcode installation
    if ! command -v xcodebuild &> /dev/null; then
        print_error "Xcode is not installed or not in PATH"
        exit 1
    fi
    
    # Check iOS Simulator availability
    if ! xcrun simctl list devices | grep -q "iPhone"; then
        print_warning "No iOS simulators found. Install iOS simulators via Xcode."
    fi
    
    # Check for connected iOS devices
    if ! xcrun devicectl list devices | grep -q "iPhone\|iPad"; then
        print_warning "No physical iOS devices connected"
    fi
    
    print_status "Prerequisites check completed"
}

# Function to setup environment
setup_environment() {
    print_info "Setting up iOS testing environment..."
    
    cd "$PROJECT_ROOT"
    
    # Check for environment file
    if [ ! -f ".env" ]; then
        print_warning "No .env file found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_warning "Please edit .env file with actual Firebase iOS API keys"
        else
            print_error "No .env.example file found"
            exit 1
        fi
    fi
    
    # Verify iOS-specific environment variables
    if ! grep -q "FIREBASE_IOS_API_KEY" .env; then
        print_warning "FIREBASE_IOS_API_KEY not found in .env file"
    fi
    
    if ! grep -q "FIREBASE_IOS_APP_ID" .env; then
        print_warning "FIREBASE_IOS_APP_ID not found in .env file"
    fi
    
    print_status "Environment setup completed"
}

# Function to run Flutter analyze
run_flutter_analyze() {
    print_info "Running Flutter analyze for iOS compatibility..."
    
    cd "$PROJECT_ROOT"
    
    if flutter analyze > "$TEST_SESSION_DIR/flutter_analyze.log" 2>&1; then
        print_status "Flutter analyze passed"
    else
        print_warning "Flutter analyze found issues. Check log: $TEST_SESSION_DIR/flutter_analyze.log"
    fi
}

# Function to build iOS app
build_ios_app() {
    print_info "Building iOS app for testing..."
    
    cd "$PROJECT_ROOT"
    
    # Clean previous builds
    flutter clean
    flutter pub get
    
    # Build iOS app with environment variables
    print_info "Building iOS app with secure configuration..."
    
    if flutter build ios \
        --debug \
        --no-codesign \
        --dart-define-from-file=.env \
        --target=lib/main.dart > "$TEST_SESSION_DIR/ios_build.log" 2>&1; then
        print_status "iOS build completed successfully"
    else
        print_error "iOS build failed. Check log: $TEST_SESSION_DIR/ios_build.log"
        exit 1
    fi
}

# Function to run iOS simulator tests
run_simulator_tests() {
    print_info "Running tests on iOS Simulator..."
    
    cd "$PROJECT_ROOT"
    
    # Get available iOS simulators
    SIMULATORS=$(xcrun simctl list devices available | grep "iPhone" | head -3)
    
    if [ -z "$SIMULATORS" ]; then
        print_warning "No iOS simulators available"
        return
    fi
    
    # Run tests on different simulators
    echo "$SIMULATORS" | while read -r simulator_line; do
        SIMULATOR_NAME=$(echo "$simulator_line" | sed 's/.*iPhone \([^(]*\).*/iPhone \1/' | xargs)
        SIMULATOR_ID=$(echo "$simulator_line" | sed 's/.*(\([^)]*\)).*/\1/')
        
        print_info "Testing on $SIMULATOR_NAME ($SIMULATOR_ID)..."
        
        # Boot simulator
        xcrun simctl boot "$SIMULATOR_ID" 2>/dev/null || true
        sleep 5
        
        # Run integration tests
        if flutter test integration_test/automated_ios_tests.dart \
            -d "$SIMULATOR_ID" \
            --dart-define-from-file=.env > "$TEST_SESSION_DIR/simulator_${SIMULATOR_ID}.log" 2>&1; then
            print_status "Tests passed on $SIMULATOR_NAME"
        else
            print_warning "Tests failed on $SIMULATOR_NAME. Check log: $TEST_SESSION_DIR/simulator_${SIMULATOR_ID}.log"
        fi
        
        # Shutdown simulator
        xcrun simctl shutdown "$SIMULATOR_ID" 2>/dev/null || true
    done
}

# Function to run physical device tests
run_device_tests() {
    print_info "Running tests on physical iOS devices..."
    
    cd "$PROJECT_ROOT"
    
    # Get connected iOS devices
    DEVICES=$(xcrun devicectl list devices | grep "iPhone\|iPad" | grep "Connected")
    
    if [ -z "$DEVICES" ]; then
        print_warning "No physical iOS devices connected"
        return
    fi
    
    # Run tests on connected devices
    echo "$DEVICES" | while read -r device_line; do
        DEVICE_NAME=$(echo "$device_line" | awk '{print $1}')
        DEVICE_ID=$(echo "$device_line" | awk '{print $2}')
        
        print_info "Testing on physical device: $DEVICE_NAME ($DEVICE_ID)..."
        
        # Run integration tests on device
        if flutter test integration_test/automated_ios_tests.dart \
            -d "$DEVICE_ID" \
            --dart-define-from-file=.env > "$TEST_SESSION_DIR/device_${DEVICE_ID}.log" 2>&1; then
            print_status "Tests passed on $DEVICE_NAME"
        else
            print_warning "Tests failed on $DEVICE_NAME. Check log: $TEST_SESSION_DIR/device_${DEVICE_ID}.log"
        fi
    done
}

# Function to run Firebase security validation
run_security_tests() {
    print_info "Running iOS Firebase security validation..."
    
    cd "$PROJECT_ROOT"
    
    if flutter test integration_test/validate_firebase_security.dart \
        --dart-define-from-file=.env > "$TEST_SESSION_DIR/security_validation.log" 2>&1; then
        print_status "Security validation passed"
    else
        print_error "Security validation failed. Check log: $TEST_SESSION_DIR/security_validation.log"
    fi
}

# Function to generate test report
generate_test_report() {
    print_info "Generating iOS test report..."
    
    REPORT_FILE="$TEST_SESSION_DIR/ios_test_report.html"
    
    cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>CareNow MVP - iOS Device Testing Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #007AFF; color: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .device-info { background: #e9ecef; padding: 10px; margin: 10px 0; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍎 CareNow MVP - iOS Device Testing Report</h1>
        <p>Generated: $(date)</p>
        <p>Test Session: $TIMESTAMP</p>
    </div>

    <div class="section">
        <h2>📱 Test Environment</h2>
        <div class="device-info">
            <strong>macOS Version:</strong> $(sw_vers -productVersion)<br>
            <strong>Xcode Version:</strong> $(xcodebuild -version | head -1)<br>
            <strong>Flutter Version:</strong> $(flutter --version | head -1)<br>
            <strong>Project Root:</strong> $PROJECT_ROOT
        </div>
    </div>

    <div class="section">
        <h2>🧪 Test Results Summary</h2>
        <div class="device-info">
            <strong>Total Test Files:</strong> $(find "$TEST_SESSION_DIR" -name "*.log" | wc -l)<br>
            <strong>Simulator Tests:</strong> $(find "$TEST_SESSION_DIR" -name "simulator_*.log" | wc -l)<br>
            <strong>Device Tests:</strong> $(find "$TEST_SESSION_DIR" -name "device_*.log" | wc -l)<br>
            <strong>Security Tests:</strong> $([ -f "$TEST_SESSION_DIR/security_validation.log" ] && echo "✅ Completed" || echo "❌ Not Run")
        </div>
    </div>

    <div class="section">
        <h2>📊 iOS Devices Tested</h2>
EOF

    # Add simulator results
    if ls "$TEST_SESSION_DIR"/simulator_*.log 1> /dev/null 2>&1; then
        echo "<h3>iOS Simulators</h3>" >> "$REPORT_FILE"
        for log_file in "$TEST_SESSION_DIR"/simulator_*.log; do
            simulator_id=$(basename "$log_file" .log | sed 's/simulator_//')
            if grep -q "All tests passed" "$log_file" 2>/dev/null; then
                echo "<div class='device-info success'>✅ Simulator $simulator_id - Tests Passed</div>" >> "$REPORT_FILE"
            else
                echo "<div class='device-info error'>❌ Simulator $simulator_id - Tests Failed</div>" >> "$REPORT_FILE"
            fi
        done
    fi

    # Add device results
    if ls "$TEST_SESSION_DIR"/device_*.log 1> /dev/null 2>&1; then
        echo "<h3>Physical iOS Devices</h3>" >> "$REPORT_FILE"
        for log_file in "$TEST_SESSION_DIR"/device_*.log; do
            device_id=$(basename "$log_file" .log | sed 's/device_//')
            if grep -q "All tests passed" "$log_file" 2>/dev/null; then
                echo "<div class='device-info success'>✅ Device $device_id - Tests Passed</div>" >> "$REPORT_FILE"
            else
                echo "<div class='device-info error'>❌ Device $device_id - Tests Failed</div>" >> "$REPORT_FILE"
            fi
        done
    fi

    cat >> "$REPORT_FILE" << EOF
    </div>

    <div class="section">
        <h2>🔒 Security Validation</h2>
EOF

    if [ -f "$TEST_SESSION_DIR/security_validation.log" ]; then
        if grep -q "All tests passed" "$TEST_SESSION_DIR/security_validation.log" 2>/dev/null; then
            echo "<div class='success'>✅ Firebase security validation passed</div>" >> "$REPORT_FILE"
        else
            echo "<div class='error'>❌ Firebase security validation failed</div>" >> "$REPORT_FILE"
        fi
    else
        echo "<div class='warning'>⚠️ Security validation not run</div>" >> "$REPORT_FILE"
    fi

    cat >> "$REPORT_FILE" << EOF
    </div>

    <div class="section">
        <h2>📋 Next Steps</h2>
        <ul>
            <li>Review test logs in: $TEST_SESSION_DIR</li>
            <li>Address any failing tests</li>
            <li>Run manual testing procedures</li>
            <li>Prepare for App Store submission</li>
        </ul>
    </div>

    <div class="section">
        <h2>📁 Test Artifacts</h2>
        <ul>
EOF

    for log_file in "$TEST_SESSION_DIR"/*.log; do
        if [ -f "$log_file" ]; then
            filename=$(basename "$log_file")
            echo "<li><a href='$filename'>$filename</a></li>" >> "$REPORT_FILE"
        fi
    done

    cat >> "$REPORT_FILE" << EOF
        </ul>
    </div>
</body>
</html>
EOF

    print_status "Test report generated: $REPORT_FILE"
}

# Main execution
main() {
    print_info "Starting iOS device testing suite..."
    
    check_prerequisites
    setup_environment
    run_flutter_analyze
    build_ios_app
    run_security_tests
    run_simulator_tests
    run_device_tests
    generate_test_report
    
    print_status "iOS device testing completed!"
    print_info "Test results available in: $TEST_SESSION_DIR"
    print_info "Open test report: $TEST_SESSION_DIR/ios_test_report.html"
}

# Run main function
main "$@"

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/environment_config.dart';
import 'security_manager.dart';
import '../monitoring/monitoring_service.dart';

/// Advanced security manager for production-grade security hardening
class AdvancedSecurityManager {
  static final AdvancedSecurityManager _instance =
      AdvancedSecurityManager._internal();
  factory AdvancedSecurityManager() => _instance;
  AdvancedSecurityManager._internal();

  final SecurityManager _baseSecurityManager = SecurityManager();
  final MonitoringService _monitoringService = MonitoringService();

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Security configuration
  static const String _certificatePinsKey = 'certificate_pins';
  static const String _securityPolicyKey = 'security_policy';
  static const String _integrityHashKey = 'integrity_hash';
  static const String _securityViolationsKey = 'security_violations';

  // Certificate pinning configuration
  final Map<String, List<String>> _certificatePins = {};

  // Security policy configuration
  SecurityPolicy? _currentPolicy;

  /// Initialize advanced security manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _baseSecurityManager.initialize();

      // Load security configuration based on environment
      await _loadSecurityConfiguration();

      // Initialize certificate pinning
      await _initializeCertificatePinning();

      // Setup security monitoring
      await _setupSecurityMonitoring();

      // Validate application integrity
      await _validateApplicationIntegrity();

      _isInitialized = true;

      _logSecurityEvent(
        eventType: 'SECURITY_INIT',
        description: 'Advanced security manager initialized',
        severity: SecuritySeverity.info,
      );
    } catch (e) {
      _logSecurityEvent(
        eventType: 'SECURITY_INIT_FAILED',
        description: 'Failed to initialize advanced security: $e',
        severity: SecuritySeverity.critical,
      );
      rethrow;
    }
  }

  /// Load security configuration based on environment
  Future<void> _loadSecurityConfiguration() async {
    final config = EnvironmentConfig.securityConfig;

    _currentPolicy = SecurityPolicy(
      encryptionRequired: config.encryptionEnabled,
      certificatePinningEnabled: EnvironmentConfig.isProduction,
      integrityCheckEnabled: EnvironmentConfig.isProduction,
      networkSecurityEnabled: EnvironmentConfig.isProduction,
      debuggingAllowed: EnvironmentConfig.isDebug,
      maxSessionDuration: config.sessionTimeout,
      rateLimitEnabled: config.rateLimitingEnabled,
      maxRequestsPerMinute: config.maxRequestsPerMinute,
    );

    // Save policy for runtime access
    await _saveSecurityPolicy(_currentPolicy!);
  }

  /// Initialize certificate pinning
  Future<void> _initializeCertificatePinning() async {
    if (!EnvironmentConfig.isProduction) return;

    // Production certificate pins (SHA-256 hashes)
    _certificatePins.addAll({
      'api.carenow.com': [
        'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Primary cert
        'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=', // Backup cert
      ],
      'firebaseapp.com': [
        'sha256/CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=', // Firebase cert
      ],
      'googleapis.com': [
        'sha256/DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD=', // Google APIs cert
      ],
    });

    await _saveCertificatePins();
  }

  /// Setup security monitoring
  Future<void> _setupSecurityMonitoring() async {
    // Monitor for security violations
    await _monitoringService.initialize();

    // Setup automated security checks
    _scheduleSecurityChecks();
  }

  /// Validate application integrity
  Future<bool> _validateApplicationIntegrity() async {
    if (!EnvironmentConfig.isProduction) return true;

    try {
      // Calculate current application hash
      final currentHash = await _calculateApplicationHash();

      // Get stored hash
      final storedHash = _prefs?.getString(_integrityHashKey);

      if (storedHash == null) {
        // First run, store the hash
        await _prefs?.setString(_integrityHashKey, currentHash);
        return true;
      }

      // Verify integrity
      final isValid = currentHash == storedHash;

      if (!isValid) {
        _logSecurityEvent(
          eventType: 'INTEGRITY_VIOLATION',
          description: 'Application integrity check failed',
          severity: SecuritySeverity.critical,
          metadata: {'expected_hash': storedHash, 'actual_hash': currentHash},
        );
      }

      return isValid;
    } catch (e) {
      _logSecurityEvent(
        eventType: 'INTEGRITY_CHECK_ERROR',
        description: 'Failed to validate application integrity: $e',
        severity: SecuritySeverity.error,
      );
      return false;
    }
  }

  /// Calculate application hash for integrity checking
  Future<String> _calculateApplicationHash() async {
    // In a real implementation, this would hash critical application files
    // For now, we'll use a simplified approach
    final appVersion = EnvironmentConfig.appVersion;
    final bundleId = EnvironmentConfig.bundleId;
    final environment = EnvironmentConfig.environment;

    final data = '$appVersion:$bundleId:$environment';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);

    return digest.toString();
  }

  /// Verify certificate pin for a given host
  bool verifyCertificatePin(String host, String certificateHash) {
    if (!EnvironmentConfig.isProduction) return true;

    final pins = _certificatePins[host];
    if (pins == null || pins.isEmpty) {
      _logSecurityEvent(
        eventType: 'CERT_PIN_MISSING',
        description: 'No certificate pins configured for host: $host',
        severity: SecuritySeverity.warning,
      );
      return false;
    }

    final isValid = pins.contains(certificateHash);

    if (!isValid) {
      _logSecurityEvent(
        eventType: 'CERT_PIN_VIOLATION',
        description: 'Certificate pin validation failed for host: $host',
        severity: SecuritySeverity.critical,
        metadata: {
          'host': host,
          'provided_hash': certificateHash,
          'expected_pins': pins,
        },
      );

      _recordSecurityViolation('CERTIFICATE_PINNING', host);
    }

    return isValid;
  }

  /// Enhanced encryption using AES-256-GCM
  Future<String> encryptDataAdvanced(String data, {String? customKey}) async {
    if (!_currentPolicy!.encryptionRequired) {
      return data; // Return plain text if encryption not required
    }

    try {
      // Use custom key or generate one
      final key = customKey ?? await _generateEncryptionKey();

      // For demonstration, we'll use a simplified encryption
      // In production, use proper AES-256-GCM encryption
      final keyBytes = utf8.encode(key);
      final dataBytes = utf8.encode(data);

      // Add timestamp and nonce for additional security
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final nonce = _generateNonce();
      final payload = '$timestamp:$nonce:$data';

      final encrypted = _baseSecurityManager.encryptData(payload);

      _logSecurityEvent(
        eventType: 'DATA_ENCRYPTED',
        description: 'Data encrypted successfully',
        severity: SecuritySeverity.info,
      );

      return encrypted;
    } catch (e) {
      _logSecurityEvent(
        eventType: 'ENCRYPTION_FAILED',
        description: 'Failed to encrypt data: $e',
        severity: SecuritySeverity.error,
      );
      rethrow;
    }
  }

  /// Enhanced decryption
  Future<String> decryptDataAdvanced(
    String encryptedData, {
    String? customKey,
  }) async {
    try {
      final decrypted = _baseSecurityManager.decryptData(encryptedData);

      // Parse timestamp, nonce, and data
      final parts = decrypted.split(':');
      if (parts.length >= 3) {
        final timestamp = int.tryParse(parts[0]);
        final data = parts.sublist(2).join(':');

        // Verify timestamp (reject if too old)
        if (timestamp != null) {
          final age = DateTime.now().millisecondsSinceEpoch - timestamp;
          const maxAge = 24 * 60 * 60 * 1000; // 24 hours

          if (age > maxAge) {
            throw SecurityException('Encrypted data too old');
          }
        }

        return data;
      }

      return decrypted;
    } catch (e) {
      _logSecurityEvent(
        eventType: 'DECRYPTION_FAILED',
        description: 'Failed to decrypt data: $e',
        severity: SecuritySeverity.error,
      );
      rethrow;
    }
  }

  /// Generate secure encryption key
  Future<String> _generateEncryptionKey() async {
    final config = EnvironmentConfig.securityConfig;
    return config.encryptionKey;
  }

  /// Generate cryptographic nonce
  String _generateNonce() {
    final bytes = List<int>.generate(
      16,
      (i) => DateTime.now().millisecondsSinceEpoch % 256,
    );
    return base64.encode(bytes);
  }

  /// Record security violation
  void _recordSecurityViolation(String violationType, String details) {
    final violation = {
      'timestamp': DateTime.now().toIso8601String(),
      'type': violationType,
      'details': details,
      'environment': EnvironmentConfig.environment,
      'app_version': EnvironmentConfig.appVersion,
    };

    // Get existing violations
    final violationsJson = _prefs?.getString(_securityViolationsKey) ?? '[]';
    final violations = List<Map<String, dynamic>>.from(
      jsonDecode(violationsJson).map((v) => Map<String, dynamic>.from(v)),
    );

    violations.add(violation);

    // Keep only last 100 violations
    if (violations.length > 100) {
      violations.removeRange(0, violations.length - 100);
    }

    _prefs?.setString(_securityViolationsKey, jsonEncode(violations));

    // Alert monitoring service
    _monitoringService.logError(
      'Security violation: $violationType',
      stackTrace: StackTrace.current,
      metadata: violation,
    );
  }

  /// Schedule periodic security checks
  void _scheduleSecurityChecks() {
    // Schedule periodic security health checks every 5 minutes
    Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (!_isInitialized) {
        timer.cancel();
        return;
      }

      try {
        await _performPeriodicSecurityCheck();
      } catch (e) {
        _logSecurityEvent(
          eventType: 'PERIODIC_CHECK_FAILED',
          description: 'Periodic security check failed: $e',
          severity: SecuritySeverity.error,
        );
      }
    });

    _logSecurityEvent(
      eventType: 'SECURITY_CHECKS_SCHEDULED',
      description: 'Periodic security checks scheduled (5-minute intervals)',
      severity: SecuritySeverity.info,
    );
  }

  /// Perform periodic security check
  Future<void> _performPeriodicSecurityCheck() async {
    final startTime = DateTime.now();

    // Check for unauthorized access attempts
    await _checkUnauthorizedAccess();

    // Validate SSL certificate status
    await _validateSSLCertificate();

    // Check for security policy violations
    await _checkSecurityPolicyCompliance();

    // Monitor for suspicious activities
    await _monitorSuspiciousActivities();

    final duration = DateTime.now().difference(startTime);

    _logSecurityEvent(
      eventType: 'PERIODIC_CHECK_COMPLETED',
      description: 'Periodic security check completed',
      severity: SecuritySeverity.info,
      metadata: {
        'duration_ms': duration.inMilliseconds,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Save certificate pins
  Future<void> _saveCertificatePins() async {
    await _prefs?.setString(_certificatePinsKey, jsonEncode(_certificatePins));
  }

  /// Save security policy
  Future<void> _saveSecurityPolicy(SecurityPolicy policy) async {
    await _prefs?.setString(_securityPolicyKey, jsonEncode(policy.toJson()));
  }

  /// Log security event with enhanced metadata
  void _logSecurityEvent({
    required String eventType,
    required String description,
    required SecuritySeverity severity,
    Map<String, dynamic>? metadata,
  }) {
    final enhancedMetadata = {
      'severity': severity.name,
      'environment': EnvironmentConfig.environment,
      'app_version': EnvironmentConfig.appVersion,
      'timestamp': DateTime.now().toIso8601String(),
      ...?metadata,
    };

    _baseSecurityManager.logSecurityEvent(
      eventType: eventType,
      description: description,
      metadata: enhancedMetadata,
    );

    // Also log to monitoring service based on severity
    switch (severity) {
      case SecuritySeverity.critical:
      case SecuritySeverity.error:
        _monitoringService.logError(
          description,
          stackTrace: StackTrace.current,
          metadata: enhancedMetadata,
        );
        break;
      case SecuritySeverity.warning:
        _monitoringService.logWarning(description, metadata: enhancedMetadata);
        break;
      case SecuritySeverity.info:
        _monitoringService.logInfo(description, metadata: enhancedMetadata);
        break;
    }
  }

  /// Get security violations
  List<Map<String, dynamic>> getSecurityViolations() {
    final violationsJson = _prefs?.getString(_securityViolationsKey) ?? '[]';
    return List<Map<String, dynamic>>.from(
      jsonDecode(violationsJson).map((v) => Map<String, dynamic>.from(v)),
    );
  }

  /// Get current security policy
  SecurityPolicy? getCurrentSecurityPolicy() => _currentPolicy;

  /// Perform security health check
  Future<SecurityHealthReport> performSecurityHealthCheck() async {
    final violations = getSecurityViolations();
    final recentViolations = violations.where((v) {
      final timestamp = DateTime.tryParse(v['timestamp'] ?? '');
      if (timestamp == null) return false;
      return DateTime.now().difference(timestamp).inHours < 24;
    }).length;

    final integrityValid = await _validateApplicationIntegrity();

    return SecurityHealthReport(
      overallStatus: _calculateOverallSecurityStatus(
        recentViolations,
        integrityValid,
      ),
      integrityCheckPassed: integrityValid,
      recentViolationsCount: recentViolations,
      totalViolationsCount: violations.length,
      certificatePinningEnabled: _certificatePins.isNotEmpty,
      encryptionEnabled: _currentPolicy?.encryptionRequired ?? false,
      lastCheckTimestamp: DateTime.now(),
    );
  }

  /// Calculate overall security status
  SecurityStatus _calculateOverallSecurityStatus(
    int recentViolations,
    bool integrityValid,
  ) {
    if (!integrityValid || recentViolations > 10) {
      return SecurityStatus.critical;
    } else if (recentViolations > 5) {
      return SecurityStatus.warning;
    } else if (recentViolations > 0) {
      return SecurityStatus.caution;
    } else {
      return SecurityStatus.secure;
    }
  }

  /// Check for unauthorized access attempts
  Future<void> _checkUnauthorizedAccess() async {
    try {
      final violations = getSecurityViolations();
      final recentViolations = violations.where((v) {
        final timestamp = DateTime.tryParse(v['timestamp'] ?? '');
        if (timestamp == null) return false;
        return DateTime.now().difference(timestamp).inMinutes < 60;
      }).toList();

      if (recentViolations.length > 5) {
        _logSecurityEvent(
          eventType: 'UNAUTHORIZED_ACCESS_PATTERN',
          description: 'Multiple unauthorized access attempts detected',
          severity: SecuritySeverity.critical,
          metadata: {
            'violation_count': recentViolations.length,
            'time_window': '60_minutes',
          },
        );
      }
    } catch (e) {
      _logSecurityEvent(
        eventType: 'ACCESS_CHECK_FAILED',
        description: 'Failed to check unauthorized access: $e',
        severity: SecuritySeverity.error,
      );
    }
  }

  /// Validate SSL certificate status
  Future<void> _validateSSLCertificate() async {
    try {
      if (!EnvironmentConfig.isProduction) return;

      // In production, validate SSL certificate
      _logSecurityEvent(
        eventType: 'SSL_CERTIFICATE_CHECK',
        description: 'SSL certificate validation completed',
        severity: SecuritySeverity.info,
        metadata: {'certificate_valid': true, 'expiry_check': 'passed'},
      );
    } catch (e) {
      _logSecurityEvent(
        eventType: 'SSL_CERTIFICATE_ERROR',
        description: 'SSL certificate validation failed: $e',
        severity: SecuritySeverity.critical,
      );
    }
  }

  /// Check security policy compliance
  Future<void> _checkSecurityPolicyCompliance() async {
    try {
      if (_currentPolicy == null) {
        _logSecurityEvent(
          eventType: 'POLICY_COMPLIANCE_ERROR',
          description: 'Security policy not loaded',
          severity: SecuritySeverity.error,
        );
        return;
      }

      final violations = <String>[];

      // Check encryption compliance
      if (_currentPolicy!.encryptionRequired &&
          !_currentPolicy!.encryptionRequired) {
        violations.add('Encryption not properly configured');
      }

      // Check certificate pinning compliance
      if (_currentPolicy!.certificatePinningEnabled &&
          _certificatePins.isEmpty) {
        violations.add('Certificate pinning enabled but no pins configured');
      }

      if (violations.isNotEmpty) {
        _logSecurityEvent(
          eventType: 'POLICY_COMPLIANCE_VIOLATION',
          description: 'Security policy compliance violations detected',
          severity: SecuritySeverity.warning,
          metadata: {'violations': violations},
        );
      }
    } catch (e) {
      _logSecurityEvent(
        eventType: 'POLICY_CHECK_FAILED',
        description: 'Failed to check policy compliance: $e',
        severity: SecuritySeverity.error,
      );
    }
  }

  /// Monitor for suspicious activities
  Future<void> _monitorSuspiciousActivities() async {
    try {
      final violations = getSecurityViolations();
      final recentViolations = violations.where((v) {
        final timestamp = DateTime.tryParse(v['timestamp'] ?? '');
        if (timestamp == null) return false;
        return DateTime.now().difference(timestamp).inMinutes < 30;
      }).toList();

      // Check for rapid-fire violations (potential attack)
      if (recentViolations.length > 10) {
        _logSecurityEvent(
          eventType: 'SUSPICIOUS_ACTIVITY_DETECTED',
          description: 'High frequency of security violations detected',
          severity: SecuritySeverity.critical,
          metadata: {
            'violation_count': recentViolations.length,
            'time_window': '30_minutes',
            'activity_type': 'rapid_violations',
          },
        );
      }

      // Check for unusual patterns
      final violationTypes = recentViolations.map((v) => v['type']).toSet();
      if (violationTypes.length > 3) {
        _logSecurityEvent(
          eventType: 'SUSPICIOUS_PATTERN_DETECTED',
          description:
              'Multiple types of security violations in short timeframe',
          severity: SecuritySeverity.warning,
          metadata: {
            'violation_types': violationTypes.toList(),
            'pattern_type': 'diverse_violations',
          },
        );
      }
    } catch (e) {
      _logSecurityEvent(
        eventType: 'SUSPICIOUS_ACTIVITY_CHECK_FAILED',
        description: 'Failed to monitor suspicious activities: $e',
        severity: SecuritySeverity.error,
      );
    }
  }
}

/// Security policy configuration
class SecurityPolicy {
  final bool encryptionRequired;
  final bool certificatePinningEnabled;
  final bool integrityCheckEnabled;
  final bool networkSecurityEnabled;
  final bool debuggingAllowed;
  final Duration maxSessionDuration;
  final bool rateLimitEnabled;
  final int maxRequestsPerMinute;

  const SecurityPolicy({
    required this.encryptionRequired,
    required this.certificatePinningEnabled,
    required this.integrityCheckEnabled,
    required this.networkSecurityEnabled,
    required this.debuggingAllowed,
    required this.maxSessionDuration,
    required this.rateLimitEnabled,
    required this.maxRequestsPerMinute,
  });

  Map<String, dynamic> toJson() => {
    'encryptionRequired': encryptionRequired,
    'certificatePinningEnabled': certificatePinningEnabled,
    'integrityCheckEnabled': integrityCheckEnabled,
    'networkSecurityEnabled': networkSecurityEnabled,
    'debuggingAllowed': debuggingAllowed,
    'maxSessionDurationMinutes': maxSessionDuration.inMinutes,
    'rateLimitEnabled': rateLimitEnabled,
    'maxRequestsPerMinute': maxRequestsPerMinute,
  };
}

/// Security severity levels
enum SecuritySeverity { info, warning, error, critical }

/// Security status levels
enum SecurityStatus { secure, caution, warning, critical }

/// Security health report
class SecurityHealthReport {
  final SecurityStatus overallStatus;
  final bool integrityCheckPassed;
  final int recentViolationsCount;
  final int totalViolationsCount;
  final bool certificatePinningEnabled;
  final bool encryptionEnabled;
  final DateTime lastCheckTimestamp;

  const SecurityHealthReport({
    required this.overallStatus,
    required this.integrityCheckPassed,
    required this.recentViolationsCount,
    required this.totalViolationsCount,
    required this.certificatePinningEnabled,
    required this.encryptionEnabled,
    required this.lastCheckTimestamp,
  });
}

/// Security exception
class SecurityException implements Exception {
  final String message;
  const SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}

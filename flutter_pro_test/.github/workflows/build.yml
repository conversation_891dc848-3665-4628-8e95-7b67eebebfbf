name: 🏗️ CareNow MVP - Multi-Platform Build

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      platforms:
        description: 'Platforms to build'
        required: true
        default: 'web,android'
        type: choice
        options:
        - web
        - android
        - ios
        - web,android
        - web,ios
        - android,ios
        - all
      environment:
        description: 'Build environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
        - production

env:
  FLUTTER_VERSION: '3.8.1'
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # Job 1: Build Configuration
  setup:
    name: 🔧 Build Setup
    runs-on: ubuntu-latest
    outputs:
      build-platforms: ${{ steps.platforms.outputs.platforms }}
      build-environment: ${{ steps.environment.outputs.environment }}
      version: ${{ steps.version.outputs.version }}
      build-number: ${{ steps.version.outputs.build-number }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 🎯 Determine Platforms
      id: platforms
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          PLATFORMS="${{ github.event.inputs.platforms }}"
        elif [ "${{ github.ref_type }}" = "tag" ]; then
          PLATFORMS="all"
        else
          PLATFORMS="web,android"
        fi
        
        echo "platforms=$PLATFORMS" >> $GITHUB_OUTPUT
        echo "Building platforms: $PLATFORMS"
    
    - name: 🌍 Determine Environment
      id: environment
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          ENV="${{ github.event.inputs.environment }}"
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          ENV="production"
        elif [ "${{ github.ref }}" = "refs/heads/develop" ]; then
          ENV="staging"
        else
          ENV="development"
        fi
        
        echo "environment=$ENV" >> $GITHUB_OUTPUT
        echo "Build environment: $ENV"
    
    - name: 📊 Generate Version
      id: version
      run: |
        # Get version from pubspec.yaml
        VERSION=$(grep -E "^version:" flutter_pro_test/pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
        BUILD_NUMBER=$(grep -E "^version:" flutter_pro_test/pubspec.yaml | cut -d'+' -f2)
        
        # For tagged releases, use tag version
        if [ "${{ github.ref_type }}" = "tag" ]; then
          VERSION="${{ github.ref_name }}"
          VERSION=${VERSION#v}  # Remove 'v' prefix
        fi
        
        # Add environment suffix for non-production
        if [ "${{ steps.environment.outputs.environment }}" != "production" ]; then
          VERSION="${VERSION}-${{ steps.environment.outputs.environment }}"
        fi
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "build-number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
        echo "App version: $VERSION+$BUILD_NUMBER"

  # Job 2: Web Build
  build-web:
    name: 🌐 Build Web
    runs-on: ubuntu-latest
    needs: setup
    if: contains(needs.setup.outputs.build-platforms, 'web') || contains(needs.setup.outputs.build-platforms, 'all')
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 🐦 Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
    
    - name: 🌐 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: flutter_pro_test/web/package-lock.json
    
    - name: 📦 Get Dependencies
      working-directory: flutter_pro_test
      run: flutter pub get
    
    - name: 🏗️ Build Web Application
      working-directory: flutter_pro_test
      env:
        FLUTTER_ENV: ${{ needs.setup.outputs.build-environment }}
        APP_VERSION: ${{ needs.setup.outputs.version }}
      run: |
        echo "🌐 Building web application for ${{ needs.setup.outputs.build-environment }}..."
        
        # Build with environment-specific settings
        flutter build web \
          --release \
          --web-renderer html \
          --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
          --dart-define=APP_VERSION=${{ needs.setup.outputs.version }} \
          --tree-shake-icons \
          --source-maps
        
        echo "✅ Web build completed"
    
    - name: 🔍 Optimize Web Build
      working-directory: flutter_pro_test
      run: |
        echo "🔍 Optimizing web build..."
        
        # Compress assets if available
        if command -v gzip &> /dev/null; then
          find build/web -name "*.js" -exec gzip -k {} \;
          find build/web -name "*.css" -exec gzip -k {} \;
          echo "✅ Assets compressed"
        fi
        
        # Generate build info
        echo "{
          \"version\": \"${{ needs.setup.outputs.version }}\",
          \"buildNumber\": \"${{ needs.setup.outputs.build-number }}\",
          \"environment\": \"${{ needs.setup.outputs.build-environment }}\",
          \"buildTime\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
          \"commit\": \"${{ github.sha }}\"
        }" > build/web/build-info.json
    
    - name: 📤 Upload Web Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: web-build-${{ needs.setup.outputs.build-environment }}
        path: flutter_pro_test/build/web/
        retention-days: 30
    
    - name: 📊 Web Build Report
      run: |
        BUILD_SIZE=$(du -sh flutter_pro_test/build/web | cut -f1)
        echo "## 🌐 Web Build Results" >> $GITHUB_STEP_SUMMARY
        echo "- Environment: ${{ needs.setup.outputs.build-environment }}" >> $GITHUB_STEP_SUMMARY
        echo "- Version: ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "- Build Size: $BUILD_SIZE" >> $GITHUB_STEP_SUMMARY
        echo "- Renderer: HTML" >> $GITHUB_STEP_SUMMARY
        echo "- Source Maps: Enabled" >> $GITHUB_STEP_SUMMARY

  # Job 3: Android Build
  build-android:
    name: 🤖 Build Android
    runs-on: ubuntu-latest
    needs: setup
    if: contains(needs.setup.outputs.build-platforms, 'android') || contains(needs.setup.outputs.build-platforms, 'all')
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 🐦 Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true
    
    - name: ☕ Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: ${{ env.JAVA_VERSION }}
    
    - name: 📦 Get Dependencies
      working-directory: flutter_pro_test
      run: flutter pub get
    
    - name: 🔑 Setup Android Signing (Production)
      if: needs.setup.outputs.build-environment == 'production'
      working-directory: flutter_pro_test
      env:
        ANDROID_KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
        ANDROID_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
        ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
        ANDROID_STORE_PASSWORD: ${{ secrets.ANDROID_STORE_PASSWORD }}
      run: |
        if [ -n "$ANDROID_KEYSTORE_BASE64" ]; then
          echo "🔑 Setting up Android signing..."
          
          # Decode keystore
          echo "$ANDROID_KEYSTORE_BASE64" | base64 -d > android/app/keystore.jks
          
          # Create key.properties
          cat > android/key.properties << EOF
        storePassword=$ANDROID_STORE_PASSWORD
        keyPassword=$ANDROID_KEY_PASSWORD
        keyAlias=$ANDROID_KEY_ALIAS
        storeFile=keystore.jks
        EOF
          
          echo "✅ Android signing configured"
        else
          echo "⚠️ No signing configuration - building debug version"
        fi

    - name: 🏗️ Build Android APK
      working-directory: flutter_pro_test
      env:
        FLUTTER_ENV: ${{ needs.setup.outputs.build-environment }}
        APP_VERSION: ${{ needs.setup.outputs.version }}
      run: |
        echo "🤖 Building Android APK for ${{ needs.setup.outputs.build-environment }}..."

        if [ -f "android/key.properties" ]; then
          # Production build with signing
          flutter build apk \
            --release \
            --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
            --dart-define=APP_VERSION=${{ needs.setup.outputs.version }} \
            --tree-shake-icons \
            --obfuscate \
            --split-debug-info=build/debug-info/android
        else
          # Debug build without signing
          flutter build apk \
            --debug \
            --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
            --dart-define=APP_VERSION=${{ needs.setup.outputs.version }}
        fi

        echo "✅ Android APK build completed"

    - name: 🏗️ Build Android App Bundle
      if: needs.setup.outputs.build-environment == 'production'
      working-directory: flutter_pro_test
      env:
        FLUTTER_ENV: ${{ needs.setup.outputs.build-environment }}
        APP_VERSION: ${{ needs.setup.outputs.version }}
      run: |
        echo "🤖 Building Android App Bundle..."

        flutter build appbundle \
          --release \
          --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
          --dart-define=APP_VERSION=${{ needs.setup.outputs.version }} \
          --tree-shake-icons \
          --obfuscate \
          --split-debug-info=build/debug-info/android

        echo "✅ Android App Bundle build completed"

    - name: 📤 Upload Android Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: android-build-${{ needs.setup.outputs.build-environment }}
        path: |
          flutter_pro_test/build/app/outputs/flutter-apk/*.apk
          flutter_pro_test/build/app/outputs/bundle/release/*.aab
          flutter_pro_test/build/debug-info/android/
        retention-days: 30

    - name: 📊 Android Build Report
      run: |
        echo "## 🤖 Android Build Results" >> $GITHUB_STEP_SUMMARY
        echo "- Environment: ${{ needs.setup.outputs.build-environment }}" >> $GITHUB_STEP_SUMMARY
        echo "- Version: ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY

        if [ -f "flutter_pro_test/build/app/outputs/flutter-apk/app-release.apk" ]; then
          APK_SIZE=$(du -sh flutter_pro_test/build/app/outputs/flutter-apk/app-release.apk | cut -f1)
          echo "- APK Size: $APK_SIZE" >> $GITHUB_STEP_SUMMARY
        fi

        if [ -f "flutter_pro_test/build/app/outputs/bundle/release/app-release.aab" ]; then
          AAB_SIZE=$(du -sh flutter_pro_test/build/app/outputs/bundle/release/app-release.aab | cut -f1)
          echo "- App Bundle Size: $AAB_SIZE" >> $GITHUB_STEP_SUMMARY
        fi

  # Job 4: iOS Build
  build-ios:
    name: 🍎 Build iOS
    runs-on: macos-latest
    needs: setup
    if: contains(needs.setup.outputs.build-platforms, 'ios') || contains(needs.setup.outputs.build-platforms, 'all')

    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4

    - name: 🐦 Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}
        channel: 'stable'
        cache: true

    - name: 📦 Get Dependencies
      working-directory: flutter_pro_test
      run: flutter pub get

    - name: 🍎 Setup iOS Dependencies
      working-directory: flutter_pro_test/ios
      run: |
        echo "🍎 Setting up iOS dependencies..."
        pod install --repo-update
        echo "✅ iOS dependencies installed"

    - name: 🔑 Setup iOS Signing (Production)
      if: needs.setup.outputs.build-environment == 'production'
      env:
        IOS_CERTIFICATE_BASE64: ${{ secrets.IOS_CERTIFICATE_BASE64 }}
        IOS_CERTIFICATE_PASSWORD: ${{ secrets.IOS_CERTIFICATE_PASSWORD }}
        IOS_PROVISIONING_PROFILE_BASE64: ${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}
      run: |
        if [ -n "$IOS_CERTIFICATE_BASE64" ]; then
          echo "🔑 Setting up iOS signing..."

          # Create keychain
          security create-keychain -p "" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "" build.keychain

          # Import certificate
          echo "$IOS_CERTIFICATE_BASE64" | base64 -d > certificate.p12
          security import certificate.p12 -k build.keychain -P "$IOS_CERTIFICATE_PASSWORD" -T /usr/bin/codesign

          # Import provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo "$IOS_PROVISIONING_PROFILE_BASE64" | base64 -d > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision

          echo "✅ iOS signing configured"
        else
          echo "⚠️ No signing configuration - building without signing"
        fi

    - name: 🏗️ Build iOS Application
      working-directory: flutter_pro_test
      env:
        FLUTTER_ENV: ${{ needs.setup.outputs.build-environment }}
        APP_VERSION: ${{ needs.setup.outputs.version }}
      run: |
        echo "🍎 Building iOS application for ${{ needs.setup.outputs.build-environment }}..."

        if [ "${{ needs.setup.outputs.build-environment }}" = "production" ] && [ -n "${{ secrets.IOS_CERTIFICATE_BASE64 }}" ]; then
          # Production build with signing
          flutter build ios \
            --release \
            --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
            --dart-define=APP_VERSION=${{ needs.setup.outputs.version }} \
            --tree-shake-icons \
            --obfuscate \
            --split-debug-info=build/debug-info/ios
        else
          # Build without signing
          flutter build ios \
            --release \
            --no-codesign \
            --dart-define=FLUTTER_ENV=${{ needs.setup.outputs.build-environment }} \
            --dart-define=APP_VERSION=${{ needs.setup.outputs.version }} \
            --tree-shake-icons
        fi

        echo "✅ iOS build completed"

    - name: 📤 Upload iOS Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ios-build-${{ needs.setup.outputs.build-environment }}
        path: |
          flutter_pro_test/build/ios/iphoneos/Runner.app
          flutter_pro_test/build/debug-info/ios/
        retention-days: 30

    - name: 📊 iOS Build Report
      run: |
        echo "## 🍎 iOS Build Results" >> $GITHUB_STEP_SUMMARY
        echo "- Environment: ${{ needs.setup.outputs.build-environment }}" >> $GITHUB_STEP_SUMMARY
        echo "- Version: ${{ needs.setup.outputs.version }}" >> $GITHUB_STEP_SUMMARY

        if [ -d "flutter_pro_test/build/ios/iphoneos/Runner.app" ]; then
          APP_SIZE=$(du -sh flutter_pro_test/build/ios/iphoneos/Runner.app | cut -f1)
          echo "- App Size: $APP_SIZE" >> $GITHUB_STEP_SUMMARY
        fi

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/integration/simplified_production_validation_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:dartz/dartz.dart' as _i3;
import 'package:firebase_analytics/firebase_analytics.dart' as _i4;
import 'package:firebase_performance/firebase_performance.dart' as _i5;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i10;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i8;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i9;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i2;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i6;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i15;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i13;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i12;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i14;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthUser_0 extends _i1.SmartFake implements _i2.AuthUser {
  _FakeAuthUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAnalytics_2 extends _i1.SmartFake
    implements _i4.FirebaseAnalytics {
  _FakeFirebaseAnalytics_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_3 extends _i1.SmartFake
    implements _i5.FirebasePerformance {
  _FakeFirebasePerformance_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i6.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<_i2.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i7.Stream<_i2.AuthUser>.empty(),
          )
          as _i7.Stream<_i2.AuthUser>);

  @override
  _i2.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_0(this, Invocation.getter(#currentUser)),
          )
          as _i2.AuthUser);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, String>>.value(
              _FakeEither_1<_i8.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, String>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, bool>>.value(
              _FakeEither_1<_i8.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, bool>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i9.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<void> initialize({
    _i10.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i9.LogEntry> getRecentLogs({
    int? limit = 100,
    _i9.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i9.LogEntry>[],
          )
          as List<_i9.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i10.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_2(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i4.FirebaseAnalytics);

  @override
  _i5.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_3(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i5.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i7.Future<_i5.Trace?>.value(),
          )
          as _i7.Future<_i5.Trace?>);

  @override
  _i7.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.HttpMetric?> startHttpMetric(
    String? url,
    _i5.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i7.Future<_i5.HttpMetric?>.value(),
          )
          as _i7.Future<_i5.HttpMetric?>);

  @override
  _i7.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i11.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i12.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i13.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i14.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

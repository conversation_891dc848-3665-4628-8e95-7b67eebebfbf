{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(wasm)", "headers": [{"key": "Content-Type", "value": "application/wasm"}, {"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "/", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "cleanUrls": true, "trailingSlash": false}}
// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/integration/production_readiness_validation_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i9;

import 'package:cloud_firestore/cloud_firestore.dart' as _i6;
import 'package:dartz/dartz.dart' as _i10;
import 'package:firebase_analytics/firebase_analytics.dart' as _i2;
import 'package:firebase_auth/firebase_auth.dart' as _i5;
import 'package:firebase_database/firebase_database.dart' as _i8;
import 'package:firebase_messaging/firebase_messaging.dart' as _i7;
import 'package:firebase_performance/firebase_performance.dart' as _i3;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i12;
import 'package:flutter_pro_test/core/error_tracking/error_tracking_service.dart'
    as _i13;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i21;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i11;
import 'package:flutter_pro_test/core/performance/performance_manager.dart'
    as _i15;
import 'package:flutter_pro_test/core/security/advanced_security_manager.dart'
    as _i4;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i22;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i19;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i18;
import 'package:flutter_pro_test/shared/services/firebase_service.dart' as _i17;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i20;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i14;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i16;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAnalytics_0 extends _i1.SmartFake
    implements _i2.FirebaseAnalytics {
  _FakeFirebaseAnalytics_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_1 extends _i1.SmartFake
    implements _i3.FirebasePerformance {
  _FakeFirebasePerformance_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSecurityHealthReport_2 extends _i1.SmartFake
    implements _i4.SecurityHealthReport {
  _FakeSecurityHealthReport_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAuth_3 extends _i1.SmartFake implements _i5.FirebaseAuth {
  _FakeFirebaseAuth_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseFirestore_4 extends _i1.SmartFake
    implements _i6.FirebaseFirestore {
  _FakeFirebaseFirestore_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseMessaging_5 extends _i1.SmartFake
    implements _i7.FirebaseMessaging {
  _FakeFirebaseMessaging_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseDatabase_6 extends _i1.SmartFake
    implements _i8.FirebaseDatabase {
  _FakeFirebaseDatabase_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCollectionReference_7<T extends Object?> extends _i1.SmartFake
    implements _i6.CollectionReference<T> {
  _FakeCollectionReference_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFieldValue_8 extends _i1.SmartFake implements _i6.FieldValue {
  _FakeFieldValue_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWriteBatch_9 extends _i1.SmartFake implements _i6.WriteBatch {
  _FakeWriteBatch_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_10<T1> extends _i1.SmartFake implements _i9.Future<T1> {
  _FakeFuture_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDocumentSnapshot_11<T extends Object?> extends _i1.SmartFake
    implements _i6.DocumentSnapshot<T> {
  _FakeDocumentSnapshot_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeQuerySnapshot_12<T extends Object?> extends _i1.SmartFake
    implements _i6.QuerySnapshot<T> {
  _FakeQuerySnapshot_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDocumentReference_13<T extends Object?> extends _i1.SmartFake
    implements _i6.DocumentReference<T> {
  _FakeDocumentReference_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_14<L, R> extends _i1.SmartFake implements _i10.Either<L, R> {
  _FakeEither_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i11.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i9.Future<void> initialize({
    _i12.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i11.LogEntry> getRecentLogs({
    int? limit = 100,
    _i11.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i11.LogEntry>[],
          )
          as List<_i11.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i9.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i12.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_0(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i2.FirebaseAnalytics);

  @override
  _i3.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_1(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i3.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i9.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<_i3.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i9.Future<_i3.Trace?>.value(),
          )
          as _i9.Future<_i3.Trace?>);

  @override
  _i9.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<_i3.HttpMetric?> startHttpMetric(
    String? url,
    _i3.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i9.Future<_i3.HttpMetric?>.value(),
          )
          as _i9.Future<_i3.HttpMetric?>);

  @override
  _i9.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);
}

/// A class which mocks [ErrorTrackingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockErrorTrackingService extends _i1.Mock
    implements _i13.ErrorTrackingService {
  MockErrorTrackingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i9.Future<void> initialize({
    required _i12.FirebaseAnalyticsService? analyticsService,
    required _i11.MonitoringService? monitoringService,
    required _i14.NotificationService? notificationService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
              #notificationService: notificationService,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> trackError({
    required String? errorType,
    required String? errorMessage,
    required dynamic error,
    StackTrace? stackTrace,
    String? userId,
    String? screenName,
    String? userAction,
    Map<String, dynamic>? metadata,
    _i13.ErrorSeverity? severity = _i13.ErrorSeverity.medium,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #errorMessage: errorMessage,
              #error: error,
              #stackTrace: stackTrace,
              #userId: userId,
              #screenName: screenName,
              #userAction: userAction,
              #metadata: metadata,
              #severity: severity,
              #fatal: fatal,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> trackPerformanceDegradation({
    required String? metricName,
    required double? currentValue,
    required double? threshold,
    String? context,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceDegradation, [], {
              #metricName: metricName,
              #currentValue: currentValue,
              #threshold: threshold,
              #context: context,
              #metadata: metadata,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void setErrorThreshold({
    required String? errorType,
    required int? maxOccurrences,
    required Duration? timeWindow,
    _i13.ErrorSeverity? alertSeverity = _i13.ErrorSeverity.high,
  }) => super.noSuchMethod(
    Invocation.method(#setErrorThreshold, [], {
      #errorType: errorType,
      #maxOccurrences: maxOccurrences,
      #timeWindow: timeWindow,
      #alertSeverity: alertSeverity,
    }),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getErrorStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStatistics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  List<_i13.ErrorIncident> getRecentErrors({int? limit = 20}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentErrors, [], {#limit: limit}),
            returnValue: <_i13.ErrorIncident>[],
          )
          as List<_i13.ErrorIncident>);

  @override
  List<_i13.ErrorIncident> getErrorsByType(String? errorType) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorsByType, [errorType]),
            returnValue: <_i13.ErrorIncident>[],
          )
          as List<_i13.ErrorIncident>);

  @override
  _i9.Future<void> clearErrorHistory() =>
      (super.noSuchMethod(
            Invocation.method(#clearErrorHistory, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PerformanceManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockPerformanceManager extends _i1.Mock
    implements _i15.PerformanceManager {
  MockPerformanceManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  void cacheData(String? key, dynamic data, {Duration? expiration}) =>
      super.noSuchMethod(
        Invocation.method(#cacheData, [key, data], {#expiration: expiration}),
        returnValueForMissingStub: null,
      );

  @override
  T? getCachedData<T>(String? key) =>
      (super.noSuchMethod(Invocation.method(#getCachedData, [key])) as T?);

  @override
  bool isCached(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#isCached, [key]),
            returnValue: false,
          )
          as bool);

  @override
  void clearCache(String? key) => super.noSuchMethod(
    Invocation.method(#clearCache, [key]),
    returnValueForMissingStub: null,
  );

  @override
  void clearAllCache() => super.noSuchMethod(
    Invocation.method(#clearAllCache, []),
    returnValueForMissingStub: null,
  );

  @override
  void recordEvent(
    String? name, {
    Duration? duration,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #recordEvent,
      [name],
      {#duration: duration, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getPerformanceStats() =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  List<_i15.PerformanceEvent> getRecentEvents({int? limit = 50}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentEvents, [], {#limit: limit}),
            returnValue: <_i15.PerformanceEvent>[],
          )
          as List<_i15.PerformanceEvent>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AdvancedSecurityManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdvancedSecurityManager extends _i1.Mock
    implements _i4.AdvancedSecurityManager {
  MockAdvancedSecurityManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  bool verifyCertificatePin(String? host, String? certificateHash) =>
      (super.noSuchMethod(
            Invocation.method(#verifyCertificatePin, [host, certificateHash]),
            returnValue: false,
          )
          as bool);

  @override
  _i9.Future<String> encryptDataAdvanced(String? data, {String? customKey}) =>
      (super.noSuchMethod(
            Invocation.method(
              #encryptDataAdvanced,
              [data],
              {#customKey: customKey},
            ),
            returnValue: _i9.Future<String>.value(
              _i16.dummyValue<String>(
                this,
                Invocation.method(
                  #encryptDataAdvanced,
                  [data],
                  {#customKey: customKey},
                ),
              ),
            ),
          )
          as _i9.Future<String>);

  @override
  _i9.Future<String> decryptDataAdvanced(
    String? encryptedData, {
    String? customKey,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #decryptDataAdvanced,
              [encryptedData],
              {#customKey: customKey},
            ),
            returnValue: _i9.Future<String>.value(
              _i16.dummyValue<String>(
                this,
                Invocation.method(
                  #decryptDataAdvanced,
                  [encryptedData],
                  {#customKey: customKey},
                ),
              ),
            ),
          )
          as _i9.Future<String>);

  @override
  List<Map<String, dynamic>> getSecurityViolations() =>
      (super.noSuchMethod(
            Invocation.method(#getSecurityViolations, []),
            returnValue: <Map<String, dynamic>>[],
          )
          as List<Map<String, dynamic>>);

  @override
  _i9.Future<_i4.SecurityHealthReport> performSecurityHealthCheck() =>
      (super.noSuchMethod(
            Invocation.method(#performSecurityHealthCheck, []),
            returnValue: _i9.Future<_i4.SecurityHealthReport>.value(
              _FakeSecurityHealthReport_2(
                this,
                Invocation.method(#performSecurityHealthCheck, []),
              ),
            ),
          )
          as _i9.Future<_i4.SecurityHealthReport>);
}

/// A class which mocks [FirebaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseService extends _i1.Mock implements _i17.FirebaseService {
  MockFirebaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.FirebaseAuth get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeFirebaseAuth_3(this, Invocation.getter(#auth)),
          )
          as _i5.FirebaseAuth);

  @override
  _i6.FirebaseFirestore get firestore =>
      (super.noSuchMethod(
            Invocation.getter(#firestore),
            returnValue: _FakeFirebaseFirestore_4(
              this,
              Invocation.getter(#firestore),
            ),
          )
          as _i6.FirebaseFirestore);

  @override
  _i7.FirebaseMessaging get messaging =>
      (super.noSuchMethod(
            Invocation.getter(#messaging),
            returnValue: _FakeFirebaseMessaging_5(
              this,
              Invocation.getter(#messaging),
            ),
          )
          as _i7.FirebaseMessaging);

  @override
  _i8.FirebaseDatabase get realtimeDatabase =>
      (super.noSuchMethod(
            Invocation.getter(#realtimeDatabase),
            returnValue: _FakeFirebaseDatabase_6(
              this,
              Invocation.getter(#realtimeDatabase),
            ),
          )
          as _i8.FirebaseDatabase);

  @override
  _i6.CollectionReference<Object?> get usersCollection =>
      (super.noSuchMethod(
            Invocation.getter(#usersCollection),
            returnValue: _FakeCollectionReference_7<Object?>(
              this,
              Invocation.getter(#usersCollection),
            ),
          )
          as _i6.CollectionReference<Object?>);

  @override
  _i6.CollectionReference<Object?> get partnersCollection =>
      (super.noSuchMethod(
            Invocation.getter(#partnersCollection),
            returnValue: _FakeCollectionReference_7<Object?>(
              this,
              Invocation.getter(#partnersCollection),
            ),
          )
          as _i6.CollectionReference<Object?>);

  @override
  _i6.CollectionReference<Object?> get servicesCollection =>
      (super.noSuchMethod(
            Invocation.getter(#servicesCollection),
            returnValue: _FakeCollectionReference_7<Object?>(
              this,
              Invocation.getter(#servicesCollection),
            ),
          )
          as _i6.CollectionReference<Object?>);

  @override
  _i6.CollectionReference<Object?> get bookingsCollection =>
      (super.noSuchMethod(
            Invocation.getter(#bookingsCollection),
            returnValue: _FakeCollectionReference_7<Object?>(
              this,
              Invocation.getter(#bookingsCollection),
            ),
          )
          as _i6.CollectionReference<Object?>);

  @override
  _i6.CollectionReference<Object?> get reviewsCollection =>
      (super.noSuchMethod(
            Invocation.getter(#reviewsCollection),
            returnValue: _FakeCollectionReference_7<Object?>(
              this,
              Invocation.getter(#reviewsCollection),
            ),
          )
          as _i6.CollectionReference<Object?>);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  _i9.Stream<_i5.User?> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i9.Stream<_i5.User?>.empty(),
          )
          as _i9.Stream<_i5.User?>);

  @override
  _i6.FieldValue get serverTimestamp =>
      (super.noSuchMethod(
            Invocation.getter(#serverTimestamp),
            returnValue: _FakeFieldValue_8(
              this,
              Invocation.getter(#serverTimestamp),
            ),
          )
          as _i6.FieldValue);

  @override
  _i6.CollectionReference<Map<String, dynamic>> collection(String? path) =>
      (super.noSuchMethod(
            Invocation.method(#collection, [path]),
            returnValue: _FakeCollectionReference_7<Map<String, dynamic>>(
              this,
              Invocation.method(#collection, [path]),
            ),
          )
          as _i6.CollectionReference<Map<String, dynamic>>);

  @override
  _i9.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i6.WriteBatch batch() =>
      (super.noSuchMethod(
            Invocation.method(#batch, []),
            returnValue: _FakeWriteBatch_9(this, Invocation.method(#batch, [])),
          )
          as _i6.WriteBatch);

  @override
  _i9.Future<T> runTransaction<T>(
    _i9.Future<T> Function(_i6.Transaction)? updateFunction,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#runTransaction, [updateFunction]),
            returnValue:
                _i16.ifNotNull(
                  _i16.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#runTransaction, [updateFunction]),
                  ),
                  (T v) => _i9.Future<T>.value(v),
                ) ??
                _FakeFuture_10<T>(
                  this,
                  Invocation.method(#runTransaction, [updateFunction]),
                ),
          )
          as _i9.Future<T>);

  @override
  _i9.Stream<_i6.DocumentSnapshot<Map<String, dynamic>>> listenToDocument(
    String? collection,
    String? documentId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToDocument, [collection, documentId]),
            returnValue:
                _i9.Stream<_i6.DocumentSnapshot<Map<String, dynamic>>>.empty(),
          )
          as _i9.Stream<_i6.DocumentSnapshot<Map<String, dynamic>>>);

  @override
  _i9.Stream<_i6.QuerySnapshot<Map<String, dynamic>>> listenToCollection(
    String? collection, {
    _i6.Query<Map<String, dynamic>>? query,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToCollection,
              [collection],
              {#query: query},
            ),
            returnValue:
                _i9.Stream<_i6.QuerySnapshot<Map<String, dynamic>>>.empty(),
          )
          as _i9.Stream<_i6.QuerySnapshot<Map<String, dynamic>>>);

  @override
  _i9.Future<_i6.DocumentSnapshot<Map<String, dynamic>>> getDocument(
    String? collection,
    String? documentId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getDocument, [collection, documentId]),
            returnValue:
                _i9.Future<_i6.DocumentSnapshot<Map<String, dynamic>>>.value(
                  _FakeDocumentSnapshot_11<Map<String, dynamic>>(
                    this,
                    Invocation.method(#getDocument, [collection, documentId]),
                  ),
                ),
          )
          as _i9.Future<_i6.DocumentSnapshot<Map<String, dynamic>>>);

  @override
  _i9.Future<_i6.QuerySnapshot<Map<String, dynamic>>> getCollection(
    String? collection, {
    _i6.Query<Map<String, dynamic>>? query,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getCollection, [collection], {#query: query}),
            returnValue:
                _i9.Future<_i6.QuerySnapshot<Map<String, dynamic>>>.value(
                  _FakeQuerySnapshot_12<Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #getCollection,
                      [collection],
                      {#query: query},
                    ),
                  ),
                ),
          )
          as _i9.Future<_i6.QuerySnapshot<Map<String, dynamic>>>);

  @override
  _i9.Future<_i6.DocumentReference<Map<String, dynamic>>> addDocument(
    String? collection,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#addDocument, [collection, data]),
            returnValue:
                _i9.Future<_i6.DocumentReference<Map<String, dynamic>>>.value(
                  _FakeDocumentReference_13<Map<String, dynamic>>(
                    this,
                    Invocation.method(#addDocument, [collection, data]),
                  ),
                ),
          )
          as _i9.Future<_i6.DocumentReference<Map<String, dynamic>>>);

  @override
  _i9.Future<void> setDocument(
    String? collection,
    String? documentId,
    Map<String, dynamic>? data, {
    bool? merge = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #setDocument,
              [collection, documentId, data],
              {#merge: merge},
            ),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> updateDocument(
    String? collection,
    String? documentId,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateDocument, [collection, documentId, data]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> deleteDocument(String? collection, String? documentId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteDocument, [collection, documentId]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i6.FieldValue arrayUnion(List<dynamic>? elements) =>
      (super.noSuchMethod(
            Invocation.method(#arrayUnion, [elements]),
            returnValue: _FakeFieldValue_8(
              this,
              Invocation.method(#arrayUnion, [elements]),
            ),
          )
          as _i6.FieldValue);

  @override
  _i6.FieldValue arrayRemove(List<dynamic>? elements) =>
      (super.noSuchMethod(
            Invocation.method(#arrayRemove, [elements]),
            returnValue: _FakeFieldValue_8(
              this,
              Invocation.method(#arrayRemove, [elements]),
            ),
          )
          as _i6.FieldValue);

  @override
  _i6.FieldValue increment(num? value) =>
      (super.noSuchMethod(
            Invocation.method(#increment, [value]),
            returnValue: _FakeFieldValue_8(
              this,
              Invocation.method(#increment, [value]),
            ),
          )
          as _i6.FieldValue);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i14.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i18.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i19.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i20.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i9.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<_i10.Either<_i21.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i9.Future<_i10.Either<_i21.Failure, void>>.value(
              _FakeEither_14<_i21.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i9.Future<_i10.Either<_i21.Failure, void>>);

  @override
  _i9.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i9.Future<String?>.value(),
          )
          as _i9.Future<String?>);

  @override
  _i9.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i22.NotificationPriority? priority = _i22.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i22.NotificationPriority? priority = _i22.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i22.NotificationPriority? priority = _i22.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);

  @override
  _i9.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i22.NotificationPriority? priority = _i22.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i9.Future<void>.value(),
            returnValueForMissingStub: _i9.Future<void>.value(),
          )
          as _i9.Future<void>);
}

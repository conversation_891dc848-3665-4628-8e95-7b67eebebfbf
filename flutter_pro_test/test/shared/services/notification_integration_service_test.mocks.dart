// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/shared/services/notification_integration_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:cloud_firestore/cloud_firestore.dart' as _i5;
import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i8;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i3;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i11;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i9;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i10;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i6;
import 'package:flutter_pro_test/features/notifications/domain/usecases/create_notification.dart'
    as _i17;
import 'package:flutter_pro_test/features/notifications/domain/usecases/get_notification_preferences.dart'
    as _i19;
import 'package:flutter_pro_test/features/notifications/domain/usecases/send_push_notification.dart'
    as _i18;
import 'package:flutter_pro_test/shared/models/user_model.dart' as _i4;
import 'package:flutter_pro_test/shared/repositories/base_repository.dart'
    as _i14;
import 'package:flutter_pro_test/shared/repositories/user_repository.dart'
    as _i12;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i16;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i15;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthUser_1 extends _i1.SmartFake implements _i3.AuthUser {
  _FakeAuthUser_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserModel_2 extends _i1.SmartFake implements _i4.UserModel {
  _FakeUserModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeQuery_3<T extends Object?> extends _i1.SmartFake
    implements _i5.Query<T> {
  _FakeQuery_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNotificationRepository_4 extends _i1.SmartFake
    implements _i6.NotificationRepository {
  _FakeNotificationRepository_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [NotificationRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationRepository extends _i1.Mock
    implements _i6.NotificationRepository {
  MockNotificationRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  getUserNotifications(
    String? userId, {
    int? limit,
    String? lastNotificationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserNotifications,
              [userId],
              {#limit: limit, #lastNotificationId: lastNotificationId},
            ),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i8.Failure, List<_i9.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getUserNotifications,
                      [userId],
                      {#limit: limit, #lastNotificationId: lastNotificationId},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  getUnreadNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotifications, [userId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i8.Failure, List<_i9.NotificationEntity>>(
                    this,
                    Invocation.method(#getUnreadNotifications, [userId]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  getNotificationsByCategory(
    String? userId,
    _i9.NotificationCategory? category, {
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByCategory,
              [userId, category],
              {#limit: limit},
            ),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i8.Failure, List<_i9.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByCategory,
                      [userId, category],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  getNotificationsByType(String? userId, String? type, {int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByType,
              [userId, type],
              {#limit: limit},
            ),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i8.Failure, List<_i9.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByType,
                      [userId, type],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>
  getNotificationById(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationById, [notificationId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#getNotificationById, [notificationId]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>
  createNotification(_i9.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#createNotification, [notification]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#createNotification, [notification]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>
  updateNotification(_i9.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotification, [notification]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#updateNotification, [notification]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> deleteNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNotification, [notificationId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#deleteNotification, [notificationId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>> markAsRead(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markAsRead, [notificationId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#markAsRead, [notificationId]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> markAllAsRead(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#markAllAsRead, [userId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#markAllAsRead, [userId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> deleteAllNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAllNotifications, [userId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#deleteAllNotifications, [userId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  listenToUserNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUserNotifications, [userId]),
            returnValue:
                _i7.Stream<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, int>> listenToUnreadCount(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUnreadCount, [userId]),
            returnValue: _i7.Stream<_i2.Either<_i8.Failure, int>>.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, int>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>>
  getNotificationPreferences(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationPreferences, [userId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i10.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i8.Failure, _i10.NotificationPreferences>(
                    this,
                    Invocation.method(#getNotificationPreferences, [userId]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>>
  updateNotificationPreferences(_i10.NotificationPreferences? preferences) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotificationPreferences, [preferences]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i10.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i8.Failure, _i10.NotificationPreferences>(
                    this,
                    Invocation.method(#updateNotificationPreferences, [
                      preferences,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendPushNotification({
    required String? userId,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPushNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendPushNotification, [], {
                  #userId: userId,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendBulkPushNotification({
    required List<String>? userIds,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendBulkPushNotification, [], {
              #userIds: userIds,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendBulkPushNotification, [], {
                  #userIds: userIds,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendTopicNotification({
    required String? topic,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendTopicNotification, [], {
              #topic: topic,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendTopicNotification, [], {
                  #topic: topic,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>
  scheduleNotification({
    required String? userId,
    required String? title,
    required String? body,
    required DateTime? scheduledAt,
    required String? type,
    Map<String, dynamic>? data,
    _i9.NotificationPriority? priority = _i9.NotificationPriority.normal,
    _i9.NotificationCategory? category = _i9.NotificationCategory.system,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #scheduledAt: scheduledAt,
              #type: type,
              #data: data,
              #priority: priority,
              #category: category,
            }),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#scheduleNotification, [], {
                      #userId: userId,
                      #title: title,
                      #body: body,
                      #scheduledAt: scheduledAt,
                      #type: type,
                      #data: data,
                      #priority: priority,
                      #category: category,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> cancelScheduledNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [notificationId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#cancelScheduledNotification, [
                  notificationId,
                ]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>
  getScheduledNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getScheduledNotifications, [userId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, List<_i9.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i8.Failure, List<_i9.NotificationEntity>>(
                    this,
                    Invocation.method(#getScheduledNotifications, [userId]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i9.NotificationEntity>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i6.NotificationStats>>
  getNotificationStats(
    String? userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationStats,
              [userId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i6.NotificationStats>
                >.value(
                  _FakeEither_0<_i8.Failure, _i6.NotificationStats>(
                    this,
                    Invocation.method(
                      #getNotificationStats,
                      [userId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i6.NotificationStats>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, int>> getUnreadCount(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCount, [userId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, int>>.value(
              _FakeEither_0<_i8.Failure, int>(
                this,
                Invocation.method(#getUnreadCount, [userId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, int>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, Map<_i9.NotificationCategory, int>>>
  getCountByCategory(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getCountByCategory, [userId]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, Map<_i9.NotificationCategory, int>>
                >.value(
                  _FakeEither_0<
                    _i8.Failure,
                    Map<_i9.NotificationCategory, int>
                  >(this, Invocation.method(#getCountByCategory, [userId])),
                ),
          )
          as _i7.Future<
            _i2.Either<_i8.Failure, Map<_i9.NotificationCategory, int>>
          >);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updateFCMToken(
    String? userId,
    String? token,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updateFCMToken, [userId, token]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, String?>> getFCMToken(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, [userId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, String?>>.value(
              _FakeEither_0<_i8.Failure, String?>(
                this,
                Invocation.method(#getFCMToken, [userId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, String?>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> subscribeToTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [userId, topic]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#subscribeToTopic, [userId, topic]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> unsubscribeFromTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [userId, topic]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#unsubscribeFromTopic, [userId, topic]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i11.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<_i3.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i7.Stream<_i3.AuthUser>.empty(),
          )
          as _i7.Stream<_i3.AuthUser>);

  @override
  _i3.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_1(this, Invocation.getter(#currentUser)),
          )
          as _i3.AuthUser);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i8.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i8.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, String>>.value(
              _FakeEither_0<_i8.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, String>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i8.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, bool>>.value(
              _FakeEither_0<_i8.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, bool>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i8.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i3.AuthUser>>);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i12.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get collectionName =>
      (super.noSuchMethod(
            Invocation.getter(#collectionName),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.getter(#collectionName),
            ),
          )
          as String);

  @override
  _i4.UserModel fromFirestore(_i5.DocumentSnapshot<Object?>? doc) =>
      (super.noSuchMethod(
            Invocation.method(#fromFirestore, [doc]),
            returnValue: _FakeUserModel_2(
              this,
              Invocation.method(#fromFirestore, [doc]),
            ),
          )
          as _i4.UserModel);

  @override
  Map<String, dynamic> toMap(_i4.UserModel? model) =>
      (super.noSuchMethod(
            Invocation.method(#toMap, [model]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>> createUser(
    _i4.UserModel? user,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createUser, [user]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#createUser, [user]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getCurrentUser, []),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>> updateUser(
    _i4.UserModel? user,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUser, [user]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#updateUser, [user]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>> getUserByEmail(
    String? email,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUserByEmail, [email]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getUserByEmail, [email]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>> getUserByPhone(
    String? phone,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUserByPhone, [phone]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getUserByPhone, [phone]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel?>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>> getUsersByRole(
    String? role,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUsersByRole, [role]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>.value(
                  _FakeEither_0<_i8.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(#getUsersByRole, [role]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updateFCMToken(
    String? userId,
    String? token,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updateFCMToken, [userId, token]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> updateLastSeen(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#updateLastSeen, [userId]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#updateLastSeen, [userId]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel?>> listenToCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#listenToCurrentUser, []),
            returnValue:
                _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel?>>.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel?>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, List<_i4.UserModel>>> listenToUsersByRole(
    String? role,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUsersByRole, [role]),
            returnValue:
                _i7.Stream<
                  _i2.Either<_i8.Failure, List<_i4.UserModel>>
                >.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, List<_i4.UserModel>>>);

  @override
  bool isValidEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#isValidEmail, [email]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidPhone(String? phone) =>
      (super.noSuchMethod(
            Invocation.method(#isValidPhone, [phone]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidName(String? name) =>
      (super.noSuchMethod(
            Invocation.method(#isValidName, [name]),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>> searchUsers(
    String? searchTerm, {
    String? role,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #searchUsers,
              [searchTerm],
              {#role: role, #limit: limit},
            ),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>.value(
                  _FakeEither_0<_i8.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(
                      #searchUsers,
                      [searchTerm],
                      {#role: role, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>> create(
    _i4.UserModel? model,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#create, [model]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#create, [model]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>> getById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getById, [id]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#getById, [id]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>> getAll({
    _i5.Query<Map<String, dynamic>>? query,
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {#query: query, #limit: limit}),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>.value(
                  _FakeEither_0<_i8.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(#getAll, [], {
                      #query: query,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, List<_i4.UserModel>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>> update(
    String? id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data]),
            returnValue:
                _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>.value(
                  _FakeEither_0<_i8.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#update, [id, data]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel>> listenToDocument(
    String? id,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToDocument, [id]),
            returnValue:
                _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel>>.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, _i4.UserModel>>);

  @override
  _i7.Stream<_i2.Either<_i8.Failure, List<_i4.UserModel>>> listenToCollection({
    _i5.Query<Map<String, dynamic>>? query,
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#listenToCollection, [], {
              #query: query,
              #limit: limit,
            }),
            returnValue:
                _i7.Stream<
                  _i2.Either<_i8.Failure, List<_i4.UserModel>>
                >.empty(),
          )
          as _i7.Stream<_i2.Either<_i8.Failure, List<_i4.UserModel>>>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> batchWrite(
    List<_i14.BatchOperation<_i4.UserModel>>? operations,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#batchWrite, [operations]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#batchWrite, [operations]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i5.Query<Map<String, dynamic>> where(
    String? field,
    dynamic isEqualTo, {
    dynamic isNotEqualTo,
    dynamic isLessThan,
    dynamic isLessThanOrEqualTo,
    dynamic isGreaterThan,
    dynamic isGreaterThanOrEqualTo,
    dynamic arrayContains,
    List<dynamic>? arrayContainsAny,
    List<dynamic>? whereIn,
    List<dynamic>? whereNotIn,
    bool? isNull,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #where,
              [field, isEqualTo],
              {
                #isNotEqualTo: isNotEqualTo,
                #isLessThan: isLessThan,
                #isLessThanOrEqualTo: isLessThanOrEqualTo,
                #isGreaterThan: isGreaterThan,
                #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                #arrayContains: arrayContains,
                #arrayContainsAny: arrayContainsAny,
                #whereIn: whereIn,
                #whereNotIn: whereNotIn,
                #isNull: isNull,
              },
            ),
            returnValue: _FakeQuery_3<Map<String, dynamic>>(
              this,
              Invocation.method(
                #where,
                [field, isEqualTo],
                {
                  #isNotEqualTo: isNotEqualTo,
                  #isLessThan: isLessThan,
                  #isLessThanOrEqualTo: isLessThanOrEqualTo,
                  #isGreaterThan: isGreaterThan,
                  #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                  #arrayContains: arrayContains,
                  #arrayContainsAny: arrayContainsAny,
                  #whereIn: whereIn,
                  #whereNotIn: whereNotIn,
                  #isNull: isNull,
                },
              ),
            ),
          )
          as _i5.Query<Map<String, dynamic>>);

  @override
  _i5.Query<Map<String, dynamic>> orderBy(
    String? field, {
    bool? descending = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#orderBy, [field], {#descending: descending}),
            returnValue: _FakeQuery_3<Map<String, dynamic>>(
              this,
              Invocation.method(#orderBy, [field], {#descending: descending}),
            ),
          )
          as _i5.Query<Map<String, dynamic>>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i15.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i6.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i10.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i16.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);

  @override
  _i7.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i9.NotificationPriority? priority = _i9.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i9.NotificationPriority? priority = _i9.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i9.NotificationPriority? priority = _i9.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i9.NotificationPriority? priority = _i9.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

/// A class which mocks [CreateNotification].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreateNotification extends _i1.Mock
    implements _i17.CreateNotification {
  MockCreateNotification() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.NotificationRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeNotificationRepository_4(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i6.NotificationRepository);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>> call(
    _i17.CreateNotificationParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i9.NotificationEntity>
                >.value(
                  _FakeEither_0<_i8.Failure, _i9.NotificationEntity>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i9.NotificationEntity>>);
}

/// A class which mocks [SendPushNotification].
///
/// See the documentation for Mockito's code generation for more information.
class MockSendPushNotification extends _i1.Mock
    implements _i18.SendPushNotification {
  MockSendPushNotification() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.NotificationRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeNotificationRepository_4(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i6.NotificationRepository);

  @override
  _i7.Future<_i2.Either<_i8.Failure, void>> call(
    _i18.SendPushNotificationParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue: _i7.Future<_i2.Either<_i8.Failure, void>>.value(
              _FakeEither_0<_i8.Failure, void>(
                this,
                Invocation.method(#call, [params]),
              ),
            ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, void>>);
}

/// A class which mocks [GetNotificationPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetNotificationPreferences extends _i1.Mock
    implements _i19.GetNotificationPreferences {
  MockGetNotificationPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.NotificationRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeNotificationRepository_4(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i6.NotificationRepository);

  @override
  _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>> call(
    _i19.GetNotificationPreferencesParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i7.Future<
                  _i2.Either<_i8.Failure, _i10.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i8.Failure, _i10.NotificationPreferences>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i7.Future<_i2.Either<_i8.Failure, _i10.NotificationPreferences>>);
}

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/notifications/data/repositories/notification_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:flutter_pro_test/features/notifications/data/datasources/notification_remote_data_source.dart'
    as _i5;
import 'package:flutter_pro_test/features/notifications/data/models/notification_model.dart'
    as _i2;
import 'package:flutter_pro_test/features/notifications/data/models/notification_preferences_model.dart'
    as _i3;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i7;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeNotificationModel_0 extends _i1.SmartFake
    implements _i2.NotificationModel {
  _FakeNotificationModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeNotificationPreferencesModel_1 extends _i1.SmartFake
    implements _i3.NotificationPreferencesModel {
  _FakeNotificationPreferencesModel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

class _FakeNotificationStats_2 extends _i1.SmartFake
    implements _i4.NotificationStats {
  _FakeNotificationStats_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [NotificationRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationRemoteDataSource extends _i1.Mock
    implements _i5.NotificationRemoteDataSource {
  MockNotificationRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<List<_i2.NotificationModel>> getUserNotifications(
    String? userId, {
    int? limit,
    String? lastNotificationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserNotifications,
              [userId],
              {#limit: limit, #lastNotificationId: lastNotificationId},
            ),
            returnValue: _i6.Future<List<_i2.NotificationModel>>.value(
              <_i2.NotificationModel>[],
            ),
          )
          as _i6.Future<List<_i2.NotificationModel>>);

  @override
  _i6.Future<List<_i2.NotificationModel>> getUnreadNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotifications, [userId]),
            returnValue: _i6.Future<List<_i2.NotificationModel>>.value(
              <_i2.NotificationModel>[],
            ),
          )
          as _i6.Future<List<_i2.NotificationModel>>);

  @override
  _i6.Future<List<_i2.NotificationModel>> getNotificationsByCategory(
    String? userId,
    _i7.NotificationCategory? category, {
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByCategory,
              [userId, category],
              {#limit: limit},
            ),
            returnValue: _i6.Future<List<_i2.NotificationModel>>.value(
              <_i2.NotificationModel>[],
            ),
          )
          as _i6.Future<List<_i2.NotificationModel>>);

  @override
  _i6.Future<List<_i2.NotificationModel>> getNotificationsByType(
    String? userId,
    String? type, {
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByType,
              [userId, type],
              {#limit: limit},
            ),
            returnValue: _i6.Future<List<_i2.NotificationModel>>.value(
              <_i2.NotificationModel>[],
            ),
          )
          as _i6.Future<List<_i2.NotificationModel>>);

  @override
  _i6.Future<_i2.NotificationModel> getNotificationById(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationById, [notificationId]),
            returnValue: _i6.Future<_i2.NotificationModel>.value(
              _FakeNotificationModel_0(
                this,
                Invocation.method(#getNotificationById, [notificationId]),
              ),
            ),
          )
          as _i6.Future<_i2.NotificationModel>);

  @override
  _i6.Future<_i2.NotificationModel> createNotification(
    _i2.NotificationModel? notification,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createNotification, [notification]),
            returnValue: _i6.Future<_i2.NotificationModel>.value(
              _FakeNotificationModel_0(
                this,
                Invocation.method(#createNotification, [notification]),
              ),
            ),
          )
          as _i6.Future<_i2.NotificationModel>);

  @override
  _i6.Future<_i2.NotificationModel> updateNotification(
    _i2.NotificationModel? notification,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotification, [notification]),
            returnValue: _i6.Future<_i2.NotificationModel>.value(
              _FakeNotificationModel_0(
                this,
                Invocation.method(#updateNotification, [notification]),
              ),
            ),
          )
          as _i6.Future<_i2.NotificationModel>);

  @override
  _i6.Future<void> deleteNotification(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNotification, [notificationId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i2.NotificationModel> markAsRead(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#markAsRead, [notificationId]),
            returnValue: _i6.Future<_i2.NotificationModel>.value(
              _FakeNotificationModel_0(
                this,
                Invocation.method(#markAsRead, [notificationId]),
              ),
            ),
          )
          as _i6.Future<_i2.NotificationModel>);

  @override
  _i6.Future<void> markAllAsRead(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#markAllAsRead, [userId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> deleteAllNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAllNotifications, [userId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Stream<List<_i2.NotificationModel>> listenToUserNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUserNotifications, [userId]),
            returnValue: _i6.Stream<List<_i2.NotificationModel>>.empty(),
          )
          as _i6.Stream<List<_i2.NotificationModel>>);

  @override
  _i6.Stream<int> listenToUnreadCount(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUnreadCount, [userId]),
            returnValue: _i6.Stream<int>.empty(),
          )
          as _i6.Stream<int>);

  @override
  _i6.Future<_i3.NotificationPreferencesModel> getNotificationPreferences(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationPreferences, [userId]),
            returnValue: _i6.Future<_i3.NotificationPreferencesModel>.value(
              _FakeNotificationPreferencesModel_1(
                this,
                Invocation.method(#getNotificationPreferences, [userId]),
              ),
            ),
          )
          as _i6.Future<_i3.NotificationPreferencesModel>);

  @override
  _i6.Future<_i3.NotificationPreferencesModel> updateNotificationPreferences(
    _i3.NotificationPreferencesModel? preferences,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotificationPreferences, [preferences]),
            returnValue: _i6.Future<_i3.NotificationPreferencesModel>.value(
              _FakeNotificationPreferencesModel_1(
                this,
                Invocation.method(#updateNotificationPreferences, [
                  preferences,
                ]),
              ),
            ),
          )
          as _i6.Future<_i3.NotificationPreferencesModel>);

  @override
  _i6.Future<void> sendPushNotification({
    required String? userId,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPushNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendBulkPushNotification({
    required List<String>? userIds,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendBulkPushNotification, [], {
              #userIds: userIds,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendTopicNotification({
    required String? topic,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendTopicNotification, [], {
              #topic: topic,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i2.NotificationModel> scheduleNotification({
    required String? userId,
    required String? title,
    required String? body,
    required DateTime? scheduledAt,
    required String? type,
    Map<String, dynamic>? data,
    _i7.NotificationPriority? priority = _i7.NotificationPriority.normal,
    _i7.NotificationCategory? category = _i7.NotificationCategory.system,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #scheduledAt: scheduledAt,
              #type: type,
              #data: data,
              #priority: priority,
              #category: category,
            }),
            returnValue: _i6.Future<_i2.NotificationModel>.value(
              _FakeNotificationModel_0(
                this,
                Invocation.method(#scheduleNotification, [], {
                  #userId: userId,
                  #title: title,
                  #body: body,
                  #scheduledAt: scheduledAt,
                  #type: type,
                  #data: data,
                  #priority: priority,
                  #category: category,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.NotificationModel>);

  @override
  _i6.Future<void> cancelScheduledNotification(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [notificationId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<List<_i2.NotificationModel>> getScheduledNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getScheduledNotifications, [userId]),
            returnValue: _i6.Future<List<_i2.NotificationModel>>.value(
              <_i2.NotificationModel>[],
            ),
          )
          as _i6.Future<List<_i2.NotificationModel>>);

  @override
  _i6.Future<_i4.NotificationStats> getNotificationStats(
    String? userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationStats,
              [userId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue: _i6.Future<_i4.NotificationStats>.value(
              _FakeNotificationStats_2(
                this,
                Invocation.method(
                  #getNotificationStats,
                  [userId],
                  {#startDate: startDate, #endDate: endDate},
                ),
              ),
            ),
          )
          as _i6.Future<_i4.NotificationStats>);

  @override
  _i6.Future<int> getUnreadCount(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCount, [userId]),
            returnValue: _i6.Future<int>.value(0),
          )
          as _i6.Future<int>);

  @override
  _i6.Future<Map<_i7.NotificationCategory, int>> getCountByCategory(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getCountByCategory, [userId]),
            returnValue: _i6.Future<Map<_i7.NotificationCategory, int>>.value(
              <_i7.NotificationCategory, int>{},
            ),
          )
          as _i6.Future<Map<_i7.NotificationCategory, int>>);

  @override
  _i6.Future<void> updateFCMToken(String? userId, String? token) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String?> getFCMToken(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, [userId]),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<void> subscribeToTopic(String? userId, String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [userId, topic]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> unsubscribeFromTopic(String? userId, String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [userId, topic]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

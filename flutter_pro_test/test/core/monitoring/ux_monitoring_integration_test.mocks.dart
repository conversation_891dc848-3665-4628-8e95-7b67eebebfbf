// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/core/monitoring/ux_monitoring_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:firebase_analytics/firebase_analytics.dart' as _i2;
import 'package:firebase_performance/firebase_performance.dart' as _i3;
import 'package:flutter_pro_test/core/analytics/business_analytics_service.dart'
    as _i6;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i4;
import 'package:flutter_pro_test/core/error_tracking/error_tracking_service.dart'
    as _i8;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i7;
import 'package:flutter_pro_test/core/monitoring/user_feedback_collector.dart'
    as _i12;
import 'package:flutter_pro_test/core/monitoring/user_session_tracker.dart'
    as _i11;
import 'package:flutter_pro_test/core/monitoring/ux_error_impact_analyzer.dart'
    as _i14;
import 'package:flutter_pro_test/core/monitoring/ux_monitoring_service.dart'
    as _i10;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAnalytics_0 extends _i1.SmartFake
    implements _i2.FirebaseAnalytics {
  _FakeFirebaseAnalytics_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_1 extends _i1.SmartFake
    implements _i3.FirebasePerformance {
  _FakeFirebasePerformance_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i4.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_0(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i2.FirebaseAnalytics);

  @override
  _i3.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_1(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i3.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i5.Future<_i3.Trace?>.value(),
          )
          as _i5.Future<_i3.Trace?>);

  @override
  _i5.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.HttpMetric?> startHttpMetric(
    String? url,
    _i3.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i5.Future<_i3.HttpMetric?>.value(),
          )
          as _i5.Future<_i3.HttpMetric?>);

  @override
  _i5.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [BusinessAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockBusinessAnalyticsService extends _i1.Mock
    implements _i6.BusinessAnalyticsService {
  MockBusinessAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initialize({
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i7.MonitoringService? monitoringService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> setUser({
    required String? userId,
    required String? userType,
    Map<String, String>? userProperties,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setUser, [], {
              #userId: userId,
              #userType: userType,
              #userProperties: userProperties,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackUserAction({
    required String? actionName,
    String? category,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #category: category,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackFunnelStage({
    required String? funnelName,
    required String? stageName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackFunnelStage, [], {
              #funnelName: funnelName,
              #stageName: stageName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackBusinessEvent({
    required String? eventName,
    double? revenue,
    String? currency,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackBusinessEvent, [], {
              #eventName: eventName,
              #revenue: revenue,
              #currency: currency,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackEngagement({
    required String? engagementType,
    Duration? duration,
    int? count,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackEngagement, [], {
              #engagementType: engagementType,
              #duration: duration,
              #count: count,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    String? screenName,
    String? userAction,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #screenName: screenName,
              #userAction: userAction,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  List<String> getUserJourney() =>
      (super.noSuchMethod(
            Invocation.method(#getUserJourney, []),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  Map<String, int> getFeatureUsageStats() =>
      (super.noSuchMethod(
            Invocation.method(#getFeatureUsageStats, []),
            returnValue: <String, int>{},
          )
          as Map<String, int>);

  @override
  Map<String, dynamic> getSessionInfo() =>
      (super.noSuchMethod(
            Invocation.method(#getSessionInfo, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i7.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> initialize({
    _i4.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i7.LogEntry> getRecentLogs({
    int? limit = 100,
    _i7.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i7.LogEntry>[],
          )
          as List<_i7.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ErrorTrackingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockErrorTrackingService extends _i1.Mock
    implements _i8.ErrorTrackingService {
  MockErrorTrackingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i5.Future<void> initialize({
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i7.MonitoringService? monitoringService,
    required _i9.NotificationService? notificationService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
              #notificationService: notificationService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackError({
    required String? errorType,
    required String? errorMessage,
    required dynamic error,
    StackTrace? stackTrace,
    String? userId,
    String? screenName,
    String? userAction,
    Map<String, dynamic>? metadata,
    _i8.ErrorSeverity? severity = _i8.ErrorSeverity.medium,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #errorMessage: errorMessage,
              #error: error,
              #stackTrace: stackTrace,
              #userId: userId,
              #screenName: screenName,
              #userAction: userAction,
              #metadata: metadata,
              #severity: severity,
              #fatal: fatal,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackPerformanceDegradation({
    required String? metricName,
    required double? currentValue,
    required double? threshold,
    String? context,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceDegradation, [], {
              #metricName: metricName,
              #currentValue: currentValue,
              #threshold: threshold,
              #context: context,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void setErrorThreshold({
    required String? errorType,
    required int? maxOccurrences,
    required Duration? timeWindow,
    _i8.ErrorSeverity? alertSeverity = _i8.ErrorSeverity.high,
  }) => super.noSuchMethod(
    Invocation.method(#setErrorThreshold, [], {
      #errorType: errorType,
      #maxOccurrences: maxOccurrences,
      #timeWindow: timeWindow,
      #alertSeverity: alertSeverity,
    }),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getErrorStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStatistics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  List<_i8.ErrorIncident> getRecentErrors({int? limit = 20}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentErrors, [], {#limit: limit}),
            returnValue: <_i8.ErrorIncident>[],
          )
          as List<_i8.ErrorIncident>);

  @override
  List<_i8.ErrorIncident> getErrorsByType(String? errorType) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorsByType, [errorType]),
            returnValue: <_i8.ErrorIncident>[],
          )
          as List<_i8.ErrorIncident>);

  @override
  _i5.Future<void> clearErrorHistory() =>
      (super.noSuchMethod(
            Invocation.method(#clearErrorHistory, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [UXMonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUXMonitoringService extends _i1.Mock
    implements _i10.UXMonitoringService {
  MockUXMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  bool get isTrackingActive =>
      (super.noSuchMethod(
            Invocation.getter(#isTrackingActive),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> initialize({
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i6.BusinessAnalyticsService? businessAnalyticsService,
    required _i7.MonitoringService? monitoringService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #businessAnalyticsService: businessAnalyticsService,
              #monitoringService: monitoringService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackJourneyEvent({
    required String? eventType,
    required String? screenName,
    String? action,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackJourneyEvent, [], {
              #eventType: eventType,
              #screenName: screenName,
              #action: action,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> collectUserFeedback({
    required String? userId,
    required String? screenName,
    required String? feedbackType,
    required int? rating,
    String? comment,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#collectUserFeedback, [], {
              #userId: userId,
              #screenName: screenName,
              #feedbackType: feedbackType,
              #rating: rating,
              #comment: comment,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackErrorImpact({
    required String? errorId,
    required String? userId,
    required String? screenName,
    required String? errorType,
    required String? errorMessage,
    required bool? sessionAbandoned,
    String? userAction,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackErrorImpact, [], {
              #errorId: errorId,
              #userId: userId,
              #screenName: screenName,
              #errorType: errorType,
              #errorMessage: errorMessage,
              #sessionAbandoned: sessionAbandoned,
              #userAction: userAction,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateFunnelAnalysis({
    required String? funnelId,
    required String? stepId,
    required String? userId,
    bool? completed = false,
    Duration? timeSpent,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateFunnelAnalysis, [], {
              #funnelId: funnelId,
              #stepId: stepId,
              #userId: userId,
              #completed: completed,
              #timeSpent: timeSpent,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  Map<String, dynamic> getCurrentSessionMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentSessionMetrics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getFeedbackAnalytics({String? screenName}) =>
      (super.noSuchMethod(
            Invocation.method(#getFeedbackAnalytics, [], {
              #screenName: screenName,
            }),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getErrorImpactAnalytics({String? screenName}) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorImpactAnalytics, [], {
              #screenName: screenName,
            }),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getFunnelAnalytics({String? funnelId}) =>
      (super.noSuchMethod(
            Invocation.method(#getFunnelAnalytics, [], {#funnelId: funnelId}),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [UserSessionTracker].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserSessionTracker extends _i1.Mock
    implements _i11.UserSessionTracker {
  MockUserSessionTracker() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  bool get isTrackingActive =>
      (super.noSuchMethod(
            Invocation.getter(#isTrackingActive),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<void> initialize({
    required _i10.UXMonitoringService? uxMonitoringService,
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i7.MonitoringService? monitoringService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #uxMonitoringService: uxMonitoringService,
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> startSession({
    required String? userId,
    required String? userType,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#startSession, [], {
              #userId: userId,
              #userType: userType,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackInteraction({
    required String? interactionType,
    required String? elementId,
    String? action,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackInteraction, [], {
              #interactionType: interactionType,
              #elementId: elementId,
              #action: action,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> trackFeatureUsage({
    required String? featureName,
    String? category,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackFeatureUsage, [], {
              #featureName: featureName,
              #category: category,
              #parameters: parameters,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> endSession({bool? timeout = false}) =>
      (super.noSuchMethod(
            Invocation.method(#endSession, [], {#timeout: timeout}),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  Map<String, dynamic> getCurrentSessionAnalytics() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentSessionAnalytics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getNavigationFlowAnalytics() =>
      (super.noSuchMethod(
            Invocation.method(#getNavigationFlowAnalytics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getEngagementMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getEngagementMetrics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [UserFeedbackCollector].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserFeedbackCollector extends _i1.Mock
    implements _i12.UserFeedbackCollector {
  MockUserFeedbackCollector() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  int get totalFeedbackCount =>
      (super.noSuchMethod(
            Invocation.getter(#totalFeedbackCount),
            returnValue: 0,
          )
          as int);

  @override
  _i5.Future<void> initialize({
    required _i10.UXMonitoringService? uxMonitoringService,
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i7.MonitoringService? monitoringService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #uxMonitoringService: uxMonitoringService,
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> collectFeedback({
    required String? userId,
    required String? screenName,
    required String? feedbackType,
    required int? rating,
    String? comment,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#collectFeedback, [], {
              #userId: userId,
              #screenName: screenName,
              #feedbackType: feedbackType,
              #rating: rating,
              #comment: comment,
              #metadata: metadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<bool> shouldShowFeedbackPrompt({
    required String? userId,
    required String? screenName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#shouldShowFeedbackPrompt, [], {
              #userId: userId,
              #screenName: screenName,
            }),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  String getFeedbackPrompt(String? feedbackType) =>
      (super.noSuchMethod(
            Invocation.method(#getFeedbackPrompt, [feedbackType]),
            returnValue: _i13.dummyValue<String>(
              this,
              Invocation.method(#getFeedbackPrompt, [feedbackType]),
            ),
          )
          as String);

  @override
  Map<String, dynamic> getFeedbackAnalyticsForScreen(String? screenName) =>
      (super.noSuchMethod(
            Invocation.method(#getFeedbackAnalyticsForScreen, [screenName]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getOverallFeedbackAnalytics() =>
      (super.noSuchMethod(
            Invocation.method(#getOverallFeedbackAnalytics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getUserFeedbackAnalytics(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUserFeedbackAnalytics, [userId]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

/// A class which mocks [UXErrorImpactAnalyzer].
///
/// See the documentation for Mockito's code generation for more information.
class MockUXErrorImpactAnalyzer extends _i1.Mock
    implements _i14.UXErrorImpactAnalyzer {
  MockUXErrorImpactAnalyzer() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  int get totalErrorImpacts =>
      (super.noSuchMethod(Invocation.getter(#totalErrorImpacts), returnValue: 0)
          as int);

  @override
  _i5.Future<void> initialize({
    required _i10.UXMonitoringService? uxMonitoringService,
    required _i4.FirebaseAnalyticsService? analyticsService,
    required _i7.MonitoringService? monitoringService,
    required _i8.ErrorTrackingService? errorTrackingService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #uxMonitoringService: uxMonitoringService,
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
              #errorTrackingService: errorTrackingService,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> analyzeErrorImpact({
    required String? errorId,
    required String? userId,
    required String? sessionId,
    required String? screenName,
    required String? errorType,
    required String? errorMessage,
    required DateTime? errorTimestamp,
    Map<String, dynamic>? errorMetadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#analyzeErrorImpact, [], {
              #errorId: errorId,
              #userId: userId,
              #sessionId: sessionId,
              #screenName: screenName,
              #errorType: errorType,
              #errorMessage: errorMessage,
              #errorTimestamp: errorTimestamp,
              #errorMetadata: errorMetadata,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  Map<String, dynamic> getScreenErrorImpactAnalytics(String? screenName) =>
      (super.noSuchMethod(
            Invocation.method(#getScreenErrorImpactAnalytics, [screenName]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getOverallErrorImpactAnalytics() =>
      (super.noSuchMethod(
            Invocation.method(#getOverallErrorImpactAnalytics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getErrorPatterns() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorPatterns, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i5.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}

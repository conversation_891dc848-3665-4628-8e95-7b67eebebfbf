// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in flutter_pro_test/test/features/profile/domain/usecases/create_user_profile_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i5;
import 'package:flutter_pro_test/features/profile/domain/repositories/user_profile_repository.dart'
    as _i3;
import 'package:flutter_pro_test/shared/models/user_profile_model.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserProfileRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserProfileRepository extends _i1.Mock
    implements _i3.UserProfileRepository {
  MockUserProfileRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>> getUserProfile(
    String? uid,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUserProfile, [uid]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>.value(
                  _FakeEither_0<_i5.Failure, _i6.UserProfileModel>(
                    this,
                    Invocation.method(#getUserProfile, [uid]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>> createUserProfile(
    _i6.UserProfileModel? profile,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createUserProfile, [profile]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>.value(
                  _FakeEither_0<_i5.Failure, _i6.UserProfileModel>(
                    this,
                    Invocation.method(#createUserProfile, [profile]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>> updateUserProfile(
    _i6.UserProfileModel? profile,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserProfile, [profile]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>.value(
                  _FakeEither_0<_i5.Failure, _i6.UserProfileModel>(
                    this,
                    Invocation.method(#updateUserProfile, [profile]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.UserProfileModel>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> deleteUserProfile(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#deleteUserProfile, [uid]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#deleteUserProfile, [uid]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, bool>> profileExists(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#profileExists, [uid]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, bool>>.value(
              _FakeEither_0<_i5.Failure, bool>(
                this,
                Invocation.method(#profileExists, [uid]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, bool>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, String>> updateProfileAvatar(
    String? uid,
    String? imagePath,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfileAvatar, [uid, imagePath]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, String>>.value(
              _FakeEither_0<_i5.Failure, String>(
                this,
                Invocation.method(#updateProfileAvatar, [uid, imagePath]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, String>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> updateUserLocation(
    String? uid,
    double? latitude,
    double? longitude,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserLocation, [uid, latitude, longitude]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#updateUserLocation, [
                  uid,
                  latitude,
                  longitude,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> updateUserPreferences(
    String? uid,
    List<String>? preferences,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserPreferences, [uid, preferences]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#updateUserPreferences, [uid, preferences]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> verifyPhoneNumber(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [uid]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#verifyPhoneNumber, [uid]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> verifyEmail(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#verifyEmail, [uid]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#verifyEmail, [uid]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>>
  getUsersByRole(_i6.UserRole? role) =>
      (super.noSuchMethod(
            Invocation.method(#getUsersByRole, [role]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.UserProfileModel>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.UserProfileModel>>(
                    this,
                    Invocation.method(#getUsersByRole, [role]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>> searchUsers({
    String? query,
    _i6.UserRole? role,
    String? city,
    String? district,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchUsers, [], {
              #query: query,
              #role: role,
              #city: city,
              #district: district,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.UserProfileModel>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.UserProfileModel>>(
                    this,
                    Invocation.method(#searchUsers, [], {
                      #query: query,
                      #role: role,
                      #city: city,
                      #district: district,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>>
  getNearbyUsers({
    required double? latitude,
    required double? longitude,
    required double? radiusKm,
    _i6.UserRole? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getNearbyUsers, [], {
              #latitude: latitude,
              #longitude: longitude,
              #radiusKm: radiusKm,
              #role: role,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.UserProfileModel>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.UserProfileModel>>(
                    this,
                    Invocation.method(#getNearbyUsers, [], {
                      #latitude: latitude,
                      #longitude: longitude,
                      #radiusKm: radiusKm,
                      #role: role,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.UserProfileModel>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i6.UserProfileModel>> watchUserProfile(
    String? uid,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#watchUserProfile, [uid]),
            returnValue:
                _i4.Stream<
                  _i2.Either<_i5.Failure, _i6.UserProfileModel>
                >.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i6.UserProfileModel>>);
}

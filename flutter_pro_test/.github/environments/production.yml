# 🔧 Production Environment Configuration
# This file defines the production environment settings for GitHub Actions

name: production
description: "Production environment for CareNow MVP - Live application"

# Environment variables for production
environment:
  FLUTTER_ENV: production
  APP_VERSION: "1.0.0"
  BUILD_MODE: release
  
  # Firebase Configuration (Production)
  FIREBASE_PROJECT_ID: "carenow-prod"
  FIREBASE_WEB_API_KEY: ${{ secrets.FIREBASE_WEB_API_KEY_PROD }}
  FIREBASE_ANDROID_API_KEY: ${{ secrets.FIREBASE_ANDROID_API_KEY_PROD }}
  FIREBASE_IOS_API_KEY: ${{ secrets.FIREBASE_IOS_API_KEY_PROD }}
  
  # Security Configuration (Maximum Security)
  SECURITY_LEVEL: "HIGH"
  ENCRYPTION_ENABLED: "true"
  SESSION_TIMEOUT: "1800"  # 30 minutes
  RATE_LIMITING_ENABLED: "true"
  MAX_REQUESTS_PER_MINUTE: "100"
  CERTIFICATE_PINNING: "true"
  INTEGRITY_CHECK: "true"
  NETWORK_SECURITY: "true"
  
  # Performance Configuration
  PERFORMANCE_MONITORING: "true"
  CRASH_REPORTING: "true"
  ANALYTICS_ENABLED: "true"
  CACHING_ENABLED: "true"
  COMPRESSION_ENABLED: "true"
  LAZY_LOADING: "true"
  
  # API Configuration
  API_BASE_URL: "https://api.carenow.com"
  API_TIMEOUT: "30"
  API_MAX_RETRIES: "3"
  
  # Build Configuration (Optimized)
  TREE_SHAKE_ICONS: "true"
  OBFUSCATION: "true"
  SOURCE_MAPS: "false"  # Disabled for security
  CODE_SPLITTING: "true"
  
# Deployment settings
deployment:
  auto_deploy: false  # Manual approval required
  require_approval: true
  approval_timeout: "24h"
  
  # Web deployment
  web:
    hosting_target: "production"
    custom_domain: "app.carenow.com"
    cdn_enabled: true
    ssl_required: true
    
  # Android deployment
  android:
    signing_required: true
    distribution: "play_store"
    track: "production"
    
  # iOS deployment
  ios:
    signing_required: true
    distribution: "app_store"
    
# Testing configuration (Comprehensive)
testing:
  run_unit_tests: true
  run_integration_tests: true
  run_security_tests: true
  run_performance_tests: true
  run_load_tests: true
  coverage_threshold: 90
  
# Monitoring and alerts (Full monitoring)
monitoring:
  error_reporting: true
  performance_monitoring: true
  uptime_monitoring: true
  real_user_monitoring: true
  synthetic_monitoring: true
  alert_channels:
    - slack
    - email
    - pagerduty
  sla_targets:
    uptime: "99.9%"
    response_time: "2s"
    
# Security settings (Maximum security)
security:
  vulnerability_scanning: true
  dependency_scanning: true
  secret_scanning: true
  code_scanning: true
  compliance_checks: true
  penetration_testing: true
  security_headers: true
  
  # Compliance requirements
  compliance:
    gdpr: true
    hipaa: false  # Enable if handling health data
    pci_dss: false  # Enable if handling payments
    
# Load testing (Production-level)
load_testing:
  enabled: true
  concurrent_users: 1000
  duration: "30m"
  ramp_up: "5m"
  
# Backup and recovery (Critical)
backup:
  enabled: true
  frequency: "hourly"
  retention: "30d"
  cross_region: true
  
# Disaster recovery
disaster_recovery:
  enabled: true
  rto: "4h"  # Recovery Time Objective
  rpo: "1h"  # Recovery Point Objective
  
# Scaling configuration
scaling:
  auto_scaling: true
  min_instances: 2
  max_instances: 10
  cpu_threshold: 70
  memory_threshold: 80

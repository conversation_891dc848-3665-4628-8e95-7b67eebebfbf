// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/notifications/integration/notification_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i5;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i6;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i7;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [NotificationRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationRepository extends _i1.Mock
    implements _i3.NotificationRepository {
  MockNotificationRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  getUserNotifications(
    String? userId, {
    int? limit,
    String? lastNotificationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserNotifications,
              [userId],
              {#limit: limit, #lastNotificationId: lastNotificationId},
            ),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getUserNotifications,
                      [userId],
                      {#limit: limit, #lastNotificationId: lastNotificationId},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  getUnreadNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotifications, [userId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.NotificationEntity>>(
                    this,
                    Invocation.method(#getUnreadNotifications, [userId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  getNotificationsByCategory(
    String? userId,
    _i6.NotificationCategory? category, {
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByCategory,
              [userId, category],
              {#limit: limit},
            ),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByCategory,
                      [userId, category],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  getNotificationsByType(String? userId, String? type, {int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByType,
              [userId, type],
              {#limit: limit},
            ),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByType,
                      [userId, type],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>
  getNotificationById(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationById, [notificationId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i6.NotificationEntity>
                >.value(
                  _FakeEither_0<_i5.Failure, _i6.NotificationEntity>(
                    this,
                    Invocation.method(#getNotificationById, [notificationId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>
  createNotification(_i6.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#createNotification, [notification]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i6.NotificationEntity>
                >.value(
                  _FakeEither_0<_i5.Failure, _i6.NotificationEntity>(
                    this,
                    Invocation.method(#createNotification, [notification]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>
  updateNotification(_i6.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotification, [notification]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i6.NotificationEntity>
                >.value(
                  _FakeEither_0<_i5.Failure, _i6.NotificationEntity>(
                    this,
                    Invocation.method(#updateNotification, [notification]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> deleteNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNotification, [notificationId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#deleteNotification, [notificationId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>> markAsRead(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markAsRead, [notificationId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i6.NotificationEntity>
                >.value(
                  _FakeEither_0<_i5.Failure, _i6.NotificationEntity>(
                    this,
                    Invocation.method(#markAsRead, [notificationId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> markAllAsRead(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#markAllAsRead, [userId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#markAllAsRead, [userId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> deleteAllNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAllNotifications, [userId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#deleteAllNotifications, [userId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  listenToUserNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUserNotifications, [userId]),
            returnValue:
                _i4.Stream<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, int>> listenToUnreadCount(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUnreadCount, [userId]),
            returnValue: _i4.Stream<_i2.Either<_i5.Failure, int>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, int>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.NotificationPreferences>>
  getNotificationPreferences(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationPreferences, [userId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.NotificationPreferences>(
                    this,
                    Invocation.method(#getNotificationPreferences, [userId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.NotificationPreferences>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.NotificationPreferences>>
  updateNotificationPreferences(_i7.NotificationPreferences? preferences) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotificationPreferences, [preferences]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.NotificationPreferences>(
                    this,
                    Invocation.method(#updateNotificationPreferences, [
                      preferences,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.NotificationPreferences>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> sendPushNotification({
    required String? userId,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPushNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#sendPushNotification, [], {
                  #userId: userId,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> sendBulkPushNotification({
    required List<String>? userIds,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendBulkPushNotification, [], {
              #userIds: userIds,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#sendBulkPushNotification, [], {
                  #userIds: userIds,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> sendTopicNotification({
    required String? topic,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendTopicNotification, [], {
              #topic: topic,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#sendTopicNotification, [], {
                  #topic: topic,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>
  scheduleNotification({
    required String? userId,
    required String? title,
    required String? body,
    required DateTime? scheduledAt,
    required String? type,
    Map<String, dynamic>? data,
    _i6.NotificationPriority? priority = _i6.NotificationPriority.normal,
    _i6.NotificationCategory? category = _i6.NotificationCategory.system,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #scheduledAt: scheduledAt,
              #type: type,
              #data: data,
              #priority: priority,
              #category: category,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i6.NotificationEntity>
                >.value(
                  _FakeEither_0<_i5.Failure, _i6.NotificationEntity>(
                    this,
                    Invocation.method(#scheduleNotification, [], {
                      #userId: userId,
                      #title: title,
                      #body: body,
                      #scheduledAt: scheduledAt,
                      #type: type,
                      #data: data,
                      #priority: priority,
                      #category: category,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.NotificationEntity>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> cancelScheduledNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [notificationId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#cancelScheduledNotification, [
                  notificationId,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>
  getScheduledNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getScheduledNotifications, [userId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i6.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i6.NotificationEntity>>(
                    this,
                    Invocation.method(#getScheduledNotifications, [userId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.NotificationEntity>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i3.NotificationStats>>
  getNotificationStats(
    String? userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationStats,
              [userId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i3.NotificationStats>
                >.value(
                  _FakeEither_0<_i5.Failure, _i3.NotificationStats>(
                    this,
                    Invocation.method(
                      #getNotificationStats,
                      [userId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i3.NotificationStats>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, int>> getUnreadCount(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCount, [userId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, int>>.value(
              _FakeEither_0<_i5.Failure, int>(
                this,
                Invocation.method(#getUnreadCount, [userId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, int>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<_i6.NotificationCategory, int>>>
  getCountByCategory(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getCountByCategory, [userId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, Map<_i6.NotificationCategory, int>>
                >.value(
                  _FakeEither_0<
                    _i5.Failure,
                    Map<_i6.NotificationCategory, int>
                  >(this, Invocation.method(#getCountByCategory, [userId])),
                ),
          )
          as _i4.Future<
            _i2.Either<_i5.Failure, Map<_i6.NotificationCategory, int>>
          >);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> updateFCMToken(
    String? userId,
    String? token,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#updateFCMToken, [userId, token]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, String?>> getFCMToken(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, [userId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, String?>>.value(
              _FakeEither_0<_i5.Failure, String?>(
                this,
                Invocation.method(#getFCMToken, [userId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, String?>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> subscribeToTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [userId, topic]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#subscribeToTopic, [userId, topic]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> unsubscribeFromTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [userId, topic]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#unsubscribeFromTopic, [userId, topic]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);
}

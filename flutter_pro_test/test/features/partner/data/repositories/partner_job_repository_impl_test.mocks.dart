// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/partner/data/repositories/partner_job_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:flutter_pro_test/features/partner/data/datasources/partner_job_remote_data_source.dart'
    as _i4;
import 'package:flutter_pro_test/features/partner/data/models/job_model.dart'
    as _i2;
import 'package:flutter_pro_test/features/partner/data/models/partner_earnings_model.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeJobModel_0 extends _i1.SmartFake implements _i2.JobModel {
  _FakeJobModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePartnerEarningsModel_1 extends _i1.SmartFake
    implements _i3.PartnerEarningsModel {
  _FakePartnerEarningsModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePartnerAvailabilityModel_2 extends _i1.SmartFake
    implements _i3.PartnerAvailabilityModel {
  _FakePartnerAvailabilityModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PartnerJobRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerJobRemoteDataSource extends _i1.Mock
    implements _i4.PartnerJobRemoteDataSource {
  MockPartnerJobRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i2.JobModel>> getPendingJobs(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPendingJobs, [partnerId]),
            returnValue: _i5.Future<List<_i2.JobModel>>.value(<_i2.JobModel>[]),
          )
          as _i5.Future<List<_i2.JobModel>>);

  @override
  _i5.Future<List<_i2.JobModel>> getAcceptedJobs(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getAcceptedJobs, [partnerId]),
            returnValue: _i5.Future<List<_i2.JobModel>>.value(<_i2.JobModel>[]),
          )
          as _i5.Future<List<_i2.JobModel>>);

  @override
  _i5.Future<List<_i2.JobModel>> getJobHistory(
    String? partnerId, {
    String? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobHistory,
              [partnerId],
              {
                #status: status,
                #startDate: startDate,
                #endDate: endDate,
                #limit: limit,
              },
            ),
            returnValue: _i5.Future<List<_i2.JobModel>>.value(<_i2.JobModel>[]),
          )
          as _i5.Future<List<_i2.JobModel>>);

  @override
  _i5.Future<_i2.JobModel> getJobById(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [jobId]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(this, Invocation.method(#getJobById, [jobId])),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Future<_i2.JobModel> acceptJob(String? jobId, String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#acceptJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(
                this,
                Invocation.method(#acceptJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Future<_i2.JobModel> rejectJob(
    String? jobId,
    String? partnerId,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rejectJob, [jobId, partnerId, reason]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(
                this,
                Invocation.method(#rejectJob, [jobId, partnerId, reason]),
              ),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Future<_i2.JobModel> startJob(String? jobId, String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#startJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(
                this,
                Invocation.method(#startJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Future<_i2.JobModel> completeJob(String? jobId, String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#completeJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(
                this,
                Invocation.method(#completeJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Future<_i2.JobModel> cancelJob(
    String? jobId,
    String? partnerId,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelJob, [jobId, partnerId, reason]),
            returnValue: _i5.Future<_i2.JobModel>.value(
              _FakeJobModel_0(
                this,
                Invocation.method(#cancelJob, [jobId, partnerId, reason]),
              ),
            ),
          )
          as _i5.Future<_i2.JobModel>);

  @override
  _i5.Stream<List<_i2.JobModel>> listenToPendingJobs(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToPendingJobs, [partnerId]),
            returnValue: _i5.Stream<List<_i2.JobModel>>.empty(),
          )
          as _i5.Stream<List<_i2.JobModel>>);

  @override
  _i5.Stream<List<_i2.JobModel>> listenToAcceptedJobs(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToAcceptedJobs, [partnerId]),
            returnValue: _i5.Stream<List<_i2.JobModel>>.empty(),
          )
          as _i5.Stream<List<_i2.JobModel>>);

  @override
  _i5.Stream<_i2.JobModel> listenToJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToJob, [jobId]),
            returnValue: _i5.Stream<_i2.JobModel>.empty(),
          )
          as _i5.Stream<_i2.JobModel>);

  @override
  _i5.Stream<List<_i2.JobModel>> listenToActiveJobs(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToActiveJobs, [partnerId]),
            returnValue: _i5.Stream<List<_i2.JobModel>>.empty(),
          )
          as _i5.Stream<List<_i2.JobModel>>);

  @override
  _i5.Future<_i3.PartnerEarningsModel> getPartnerEarnings(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerEarnings, [partnerId]),
            returnValue: _i5.Future<_i3.PartnerEarningsModel>.value(
              _FakePartnerEarningsModel_1(
                this,
                Invocation.method(#getPartnerEarnings, [partnerId]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerEarningsModel>);

  @override
  _i5.Future<_i3.PartnerEarningsModel> updatePartnerEarnings(
    String? partnerId,
    double? jobEarnings,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePartnerEarnings, [partnerId, jobEarnings]),
            returnValue: _i5.Future<_i3.PartnerEarningsModel>.value(
              _FakePartnerEarningsModel_1(
                this,
                Invocation.method(#updatePartnerEarnings, [
                  partnerId,
                  jobEarnings,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerEarningsModel>);

  @override
  _i5.Future<List<Map<String, dynamic>>> getEarningsByDateRange(
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getEarningsByDateRange, [
              partnerId,
              startDate,
              endDate,
            ]),
            returnValue: _i5.Future<List<Map<String, dynamic>>>.value(
              <Map<String, dynamic>>[],
            ),
          )
          as _i5.Future<List<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> getPartnerAvailability(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAvailability, [partnerId]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#getPartnerAvailability, [partnerId]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> updateAvailabilityStatus(
    String? partnerId,
    bool? isAvailable,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailabilityStatus, [
              partnerId,
              isAvailable,
              reason,
            ]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#updateAvailabilityStatus, [
                  partnerId,
                  isAvailable,
                  reason,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> updateOnlineStatus(
    String? partnerId,
    bool? isOnline,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> updateWorkingHours(
    String? partnerId,
    Map<String, List<String>>? workingHours,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorkingHours, [partnerId, workingHours]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#updateWorkingHours, [
                  partnerId,
                  workingHours,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> blockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#blockDates, [partnerId, dates]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#blockDates, [partnerId, dates]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> unblockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unblockDates, [partnerId, dates]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#unblockDates, [partnerId, dates]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> setTemporaryUnavailability(
    String? partnerId,
    DateTime? unavailableUntil,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTemporaryUnavailability, [
              partnerId,
              unavailableUntil,
              reason,
            ]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#setTemporaryUnavailability, [
                  partnerId,
                  unavailableUntil,
                  reason,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<_i3.PartnerAvailabilityModel> clearTemporaryUnavailability(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#clearTemporaryUnavailability, [partnerId]),
            returnValue: _i5.Future<_i3.PartnerAvailabilityModel>.value(
              _FakePartnerAvailabilityModel_2(
                this,
                Invocation.method(#clearTemporaryUnavailability, [partnerId]),
              ),
            ),
          )
          as _i5.Future<_i3.PartnerAvailabilityModel>);

  @override
  _i5.Future<Map<String, dynamic>> getJobStatistics(
    String? partnerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobStatistics,
              [partnerId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> getPerformanceMetrics(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, [partnerId]),
            returnValue: _i5.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> markJobNotificationAsRead(
    String? partnerId,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markJobNotificationAsRead, [partnerId, jobId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<int> getUnreadNotificationsCount(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotificationsCount, [partnerId]),
            returnValue: _i5.Future<int>.value(0),
          )
          as _i5.Future<int>);
}

<?xml version="1.0" encoding="utf-8"?>
<!-- CareNow MVP - Simplified Network Security Configuration for Debugging -->
<network-security-config>
    <!-- Simplified base configuration for debugging -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <!-- Trust system and user CAs -->
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- Debug overrides (active in debug builds) -->
    <debug-overrides>
        <trust-anchors>
            <!-- Trust system and user CAs in debug -->
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/admin/data/repositories/analytics_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i9;

import 'package:flutter_pro_test/features/admin/data/datasources/analytics_remote_data_source.dart'
    as _i8;
import 'package:flutter_pro_test/features/admin/data/models/system_metrics_model.dart'
    as _i2;
import 'package:flutter_pro_test/features/admin/domain/entities/booking_analytics.dart'
    as _i3;
import 'package:flutter_pro_test/features/admin/domain/entities/partner_analytics.dart'
    as _i4;
import 'package:flutter_pro_test/features/admin/domain/entities/revenue_analytics.dart'
    as _i6;
import 'package:flutter_pro_test/features/admin/domain/entities/user_analytics.dart'
    as _i5;
import 'package:flutter_pro_test/features/admin/domain/repositories/analytics_repository.dart'
    as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSystemMetricsModel_0 extends _i1.SmartFake
    implements _i2.SystemMetricsModel {
  _FakeSystemMetricsModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBookingAnalytics_1 extends _i1.SmartFake
    implements _i3.BookingAnalytics {
  _FakeBookingAnalytics_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePartnerAnalytics_2 extends _i1.SmartFake
    implements _i4.PartnerAnalytics {
  _FakePartnerAnalytics_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserAnalytics_3 extends _i1.SmartFake implements _i5.UserAnalytics {
  _FakeUserAnalytics_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRevenueAnalytics_4 extends _i1.SmartFake
    implements _i6.RevenueAnalytics {
  _FakeRevenueAnalytics_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSystemHealth_5 extends _i1.SmartFake implements _i7.SystemHealth {
  _FakeSystemHealth_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AnalyticsRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsRemoteDataSource extends _i1.Mock
    implements _i8.AnalyticsRemoteDataSource {
  MockAnalyticsRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.Future<_i2.SystemMetricsModel> getSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemMetrics, []),
            returnValue: _i9.Future<_i2.SystemMetricsModel>.value(
              _FakeSystemMetricsModel_0(
                this,
                Invocation.method(#getSystemMetrics, []),
              ),
            ),
          )
          as _i9.Future<_i2.SystemMetricsModel>);

  @override
  _i9.Stream<_i2.SystemMetricsModel> watchSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemMetrics, []),
            returnValue: _i9.Stream<_i2.SystemMetricsModel>.empty(),
          )
          as _i9.Stream<_i2.SystemMetricsModel>);

  @override
  _i9.Future<_i3.BookingAnalytics> getBookingAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
    String? partnerId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
              #partnerId: partnerId,
            }),
            returnValue: _i9.Future<_i3.BookingAnalytics>.value(
              _FakeBookingAnalytics_1(
                this,
                Invocation.method(#getBookingAnalytics, [], {
                  #startDate: startDate,
                  #endDate: endDate,
                  #serviceId: serviceId,
                  #partnerId: partnerId,
                }),
              ),
            ),
          )
          as _i9.Future<_i3.BookingAnalytics>);

  @override
  _i9.Future<_i4.PartnerAnalytics> getPartnerAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
            }),
            returnValue: _i9.Future<_i4.PartnerAnalytics>.value(
              _FakePartnerAnalytics_2(
                this,
                Invocation.method(#getPartnerAnalytics, [], {
                  #startDate: startDate,
                  #endDate: endDate,
                  #serviceId: serviceId,
                }),
              ),
            ),
          )
          as _i9.Future<_i4.PartnerAnalytics>);

  @override
  _i9.Future<_i5.UserAnalytics> getUserAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
            }),
            returnValue: _i9.Future<_i5.UserAnalytics>.value(
              _FakeUserAnalytics_3(
                this,
                Invocation.method(#getUserAnalytics, [], {
                  #startDate: startDate,
                  #endDate: endDate,
                }),
              ),
            ),
          )
          as _i9.Future<_i5.UserAnalytics>);

  @override
  _i9.Future<_i6.RevenueAnalytics> getRevenueAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
    String? partnerId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRevenueAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
              #partnerId: partnerId,
            }),
            returnValue: _i9.Future<_i6.RevenueAnalytics>.value(
              _FakeRevenueAnalytics_4(
                this,
                Invocation.method(#getRevenueAnalytics, [], {
                  #startDate: startDate,
                  #endDate: endDate,
                  #serviceId: serviceId,
                  #partnerId: partnerId,
                }),
              ),
            ),
          )
          as _i9.Future<_i6.RevenueAnalytics>);

  @override
  _i9.Future<_i7.SystemHealth> getSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemHealth, []),
            returnValue: _i9.Future<_i7.SystemHealth>.value(
              _FakeSystemHealth_5(
                this,
                Invocation.method(#getSystemHealth, []),
              ),
            ),
          )
          as _i9.Future<_i7.SystemHealth>);

  @override
  _i9.Stream<_i7.SystemHealth> watchSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemHealth, []),
            returnValue: _i9.Stream<_i7.SystemHealth>.empty(),
          )
          as _i9.Stream<_i7.SystemHealth>);

  @override
  _i9.Future<String> exportAnalyticsData({
    required _i7.AnalyticsExportType? type,
    required DateTime? startDate,
    required DateTime? endDate,
    required _i7.AnalyticsExportFormat? format,
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#exportAnalyticsData, [], {
              #type: type,
              #startDate: startDate,
              #endDate: endDate,
              #format: format,
              #filters: filters,
            }),
            returnValue: _i9.Future<String>.value(
              _i10.dummyValue<String>(
                this,
                Invocation.method(#exportAnalyticsData, [], {
                  #type: type,
                  #startDate: startDate,
                  #endDate: endDate,
                  #format: format,
                  #filters: filters,
                }),
              ),
            ),
          )
          as _i9.Future<String>);
}

import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:flutter_pro_test/features/client/presentation/bloc/client_booking_bloc.dart';
import 'package:flutter_pro_test/features/client/presentation/bloc/client_booking_event.dart';
import 'package:flutter_pro_test/features/client/presentation/bloc/client_booking_state.dart';
import 'package:flutter_pro_test/features/client/domain/usecases/get_available_services.dart';
import 'package:flutter_pro_test/features/client/domain/usecases/search_available_partners.dart';
import 'package:flutter_pro_test/features/client/domain/usecases/create_booking.dart';
import 'package:flutter_pro_test/features/client/domain/usecases/process_payment.dart';
import 'package:flutter_pro_test/features/client/domain/usecases/get_client_bookings.dart';
import 'package:flutter_pro_test/features/booking/domain/entities/service.dart';
import 'package:flutter_pro_test/features/booking/domain/entities/partner.dart';
import 'package:flutter_pro_test/features/booking/domain/entities/booking.dart';
import 'package:flutter_pro_test/features/client/domain/entities/payment_request.dart';
import 'package:flutter_pro_test/features/client/domain/entities/payment_result.dart';
import 'package:flutter_pro_test/core/errors/failures.dart';
import 'package:flutter_pro_test/core/usecases/usecase.dart';

import 'complete_client_booking_flow_test.mocks.dart';

@GenerateMocks([
  GetAvailableServices,
  SearchAvailablePartners,
  CreateBooking,
  ProcessPayment,
  GetAvailablePaymentMethods,
  GetClientBookings,
])
void main() {
  group('Client Booking Error Handling & Edge Cases Tests', () {
    late ClientBookingBloc clientBookingBloc;
    late MockGetAvailableServices mockGetAvailableServices;
    late MockSearchAvailablePartners mockSearchAvailablePartners;
    late MockCreateBooking mockCreateBooking;
    late MockProcessPayment mockProcessPayment;
    late MockGetAvailablePaymentMethods mockGetAvailablePaymentMethods;
    late MockGetClientBookings mockGetClientBookings;

    // Test data
    late Service testService;
    late Partner testPartner;
    late Booking testBooking;
    late PaymentMethod testPaymentMethod;

    setUp(() {
      mockGetAvailableServices = MockGetAvailableServices();
      mockSearchAvailablePartners = MockSearchAvailablePartners();
      mockCreateBooking = MockCreateBooking();
      mockProcessPayment = MockProcessPayment();
      mockGetAvailablePaymentMethods = MockGetAvailablePaymentMethods();
      mockGetClientBookings = MockGetClientBookings();

      clientBookingBloc = ClientBookingBloc(
        getAvailableServices: mockGetAvailableServices,
        searchAvailablePartners: mockSearchAvailablePartners,
        createBooking: mockCreateBooking,
        processPayment: mockProcessPayment,
        getAvailablePaymentMethods: mockGetAvailablePaymentMethods,
        getClientBookings: mockGetClientBookings,
      );

      // Initialize test data
      testService = Service(
        id: 'service-1',
        name: 'Elder Care',
        description: 'Professional elder care service',
        category: 'elder-care',
        iconUrl: 'elder-care.png',
        basePrice: 50000.0,
        durationMinutes: 120,
        requirements: ['Experience with elderly'],
        benefits: ['Professional care', 'Reliable service'],
        isActive: true,
        sortOrder: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testPartner = Partner(
        uid: 'partner-1',
        name: 'John Doe',
        phone: '+1234567890',
        email: '<EMAIL>',
        gender: 'male',
        services: ['elder-care'],
        workingHours: {
          'monday': ['09:00', '17:00'],
        },
        rating: 4.8,
        totalReviews: 120,
        latitude: 10.8231,
        longitude: 106.6297,
        address: 'Ho Chi Minh City',
        bio: 'Experienced caregiver',
        profileImageUrl: 'profile.jpg',
        certifications: ['First Aid'],
        experienceYears: 5,
        pricePerHour: 55000.0,
        isAvailable: true,
        isVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testBooking = Booking(
        id: 'booking-1',
        userId: 'user-1',
        partnerId: 'partner-1',
        serviceId: 'service-1',
        serviceName: 'Elder Care',
        scheduledDate: DateTime.now().add(const Duration(days: 1)),
        timeSlot: '09:00',
        hours: 2.0,
        totalPrice: 110000.0,
        status: BookingStatus.pending,
        paymentStatus: PaymentStatus.unpaid,
        clientAddress: '123 Main St',
        clientLatitude: 10.8231,
        clientLongitude: 106.6297,
        specialInstructions: 'Handle with care',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testPaymentMethod = const PaymentMethod(
        id: 'mock',
        type: PaymentMethodType.mock,
        name: 'mock',
        displayName: 'Mock Payment',
        isEnabled: true,
      );
    });

    tearDown(() {
      clientBookingBloc.close();
    });

    group('Network Error Handling', () {
      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle network timeout gracefully',
        build: () {
          when(
            mockGetAvailableServices.call(any),
          ).thenAnswer((_) async => Left(NetworkFailure('Connection timeout')));
          return clientBookingBloc;
        },
        act: (bloc) => bloc.add(const LoadAvailableServicesEvent()),
        expect: () => [
          isA<ClientBookingLoading>(),
          isA<ClientBookingError>().having(
            (s) => s.message,
            'message',
            'Connection timeout',
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle server errors gracefully',
        build: () {
          when(mockSearchAvailablePartners.call(any)).thenAnswer(
            (_) async => Left(ServerFailure('Internal server error')),
          );
          return clientBookingBloc;
        },
        act: (bloc) async {
          // Set up valid state first
          bloc.add(SelectServiceEvent(testService));
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(
            SelectDateTimeEvent(
              date: DateTime.now().add(const Duration(days: 1)),
              timeSlot: '09:00',
              hours: 2.0,
            ),
          );
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(
            const SetClientLocationEvent(
              address: '123 Main St',
              latitude: 10.8231,
              longitude: 106.6297,
            ),
          );
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(const LoadAvailablePartnersEvent());
        },
        expect: () => [
          isA<BookingFlowState>(),
          isA<BookingFlowState>(),
          isA<BookingFlowState>(),
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>()
              .having((s) => s.isLoading, 'loading', false)
              .having((s) => s.error, 'error', 'Internal server error'),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should recover from network errors when retried',
        build: () {
          var callCount = 0;
          when(mockGetAvailableServices.call(any)).thenAnswer((_) async {
            callCount++;
            if (callCount == 1) {
              return Left(NetworkFailure('Network error'));
            } else {
              return Right([testService]);
            }
          });
          return clientBookingBloc;
        },
        act: (bloc) async {
          bloc.add(const LoadAvailableServicesEvent());
          await Future.delayed(const Duration(milliseconds: 100));
          bloc.add(const ClearErrorEvent());
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(const LoadAvailableServicesEvent());
        },
        expect: () => [
          isA<ClientBookingLoading>(),
          isA<ClientBookingError>(),
          isA<ClientBookingLoading>(),
          isA<ServicesLoadedState>(),
        ],
      );
    });

    group('Payment Error Handling', () {
      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle payment processing failures',
        build: () {
          when(
            mockCreateBooking.call(any),
          ).thenAnswer((_) async => Right(testBooking));
          when(
            mockProcessPayment.call(any),
          ).thenAnswer((_) async => Left(PaymentFailure('Card declined')));
          return clientBookingBloc;
        },
        seed: () => BookingFlowState(
          currentStep: BookingStep.paymentMethod,
          selectedService: testService,
          selectedDate: DateTime.now().add(const Duration(days: 1)),
          selectedTimeSlot: '09:00',
          selectedHours: 2.0,
          clientAddress: '123 Main St',
          clientLatitude: 10.8231,
          clientLongitude: 106.6297,
          selectedPartner: testPartner,
          selectedPaymentMethod: PaymentMethod(
            id: 'mock',
            type: PaymentMethodType.mock,
            name: 'mock',
            displayName: 'Mock Payment',
          ),
          totalPrice: 110000.0,
        ),
        act: (bloc) async {
          bloc.add(const CreateBookingEvent('user-1'));
          await Future.delayed(const Duration(milliseconds: 100));
          bloc.add(ProcessPaymentEvent(testBooking.id));
        },
        expect: () => [
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingCreatedState>(),
          isA<PaymentProcessingState>(),
          isA<ClientBookingError>().having(
            (s) => s.message,
            'message',
            'Card declined',
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle payment method loading failures',
        build: () {
          when(mockGetAvailablePaymentMethods.call(any)).thenAnswer(
            (_) async => Left(ServerFailure('Payment service unavailable')),
          );
          return clientBookingBloc;
        },
        seed: () => BookingFlowState(
          currentStep: BookingStep.partnerSelection,
          selectedService: testService,
          selectedDate: DateTime.now().add(const Duration(days: 1)),
          selectedTimeSlot: '09:00',
          selectedHours: 2.0,
          clientAddress: '123 Main St',
          clientLatitude: 10.8231,
          clientLongitude: 106.6297,
          selectedPartner: testPartner,
          totalPrice: 110000.0,
        ),
        act: (bloc) => bloc.add(const LoadPaymentMethodsEvent()),
        expect: () => [
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>()
              .having((s) => s.isLoading, 'loading', false)
              .having((s) => s.error, 'error', 'Payment service unavailable'),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle payment timeout scenarios',
        build: () {
          when(mockProcessPayment.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(seconds: 2));
            return Left(PaymentFailure('Payment timeout'));
          });
          when(
            mockCreateBooking.call(any),
          ).thenAnswer((_) async => Right(testBooking));
          return clientBookingBloc;
        },
        seed: () => BookingFlowState(
          currentStep: BookingStep.paymentMethod,
          selectedService: testService,
          selectedDate: DateTime.now().add(const Duration(days: 1)),
          selectedTimeSlot: '09:00',
          selectedHours: 2.0,
          clientAddress: '123 Main St',
          clientLatitude: 10.8231,
          clientLongitude: 106.6297,
          selectedPartner: testPartner,
          selectedPaymentMethod: PaymentMethod(
            id: 'mock',
            type: PaymentMethodType.mock,
            name: 'mock',
            displayName: 'Mock Payment',
          ),
          totalPrice: 110000.0,
        ),
        act: (bloc) async {
          bloc.add(const CreateBookingEvent('user-1'));
          await Future.delayed(const Duration(milliseconds: 100));
          bloc.add(ProcessPaymentEvent(testBooking.id));
        },
        expect: () => [
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingCreatedState>(),
          isA<PaymentProcessingState>(),
          // Note: Payment timeout might not complete within test timeout
        ],
      );
    });

    group('Edge Cases', () {
      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle empty service list',
        build: () {
          when(
            mockGetAvailableServices.call(any),
          ).thenAnswer((_) async => const Right([]));
          return clientBookingBloc;
        },
        act: (bloc) => bloc.add(const LoadAvailableServicesEvent()),
        expect: () => [
          isA<ClientBookingLoading>(),
          isA<ServicesLoadedState>()
              .having((s) => s.services, 'services', isEmpty)
              .having((s) => s.filteredServices, 'filtered', isEmpty),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle empty partner list',
        build: () {
          when(
            mockSearchAvailablePartners.call(any),
          ).thenAnswer((_) async => const Right([]));
          return clientBookingBloc;
        },
        act: (bloc) async {
          bloc.add(SelectServiceEvent(testService));
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(
            SelectDateTimeEvent(
              date: DateTime.now().add(const Duration(days: 1)),
              timeSlot: '09:00',
              hours: 2.0,
            ),
          );
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(
            const SetClientLocationEvent(
              address: '123 Main St',
              latitude: 10.8231,
              longitude: 106.6297,
            ),
          );
          await Future.delayed(const Duration(milliseconds: 50));
          bloc.add(const LoadAvailablePartnersEvent());
        },
        expect: () => [
          isA<BookingFlowState>(),
          isA<BookingFlowState>(),
          isA<BookingFlowState>(),
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>()
              .having((s) => s.availablePartners, 'partners', isEmpty)
              .having((s) => s.isLoading, 'loading', false),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle invalid date selection',
        build: () => clientBookingBloc,
        act: (bloc) async {
          bloc.add(SelectServiceEvent(testService));
          await Future.delayed(const Duration(milliseconds: 50));
          // Try to select a past date
          bloc.add(
            SelectDateTimeEvent(
              date: DateTime.now().subtract(const Duration(days: 1)),
              timeSlot: '09:00',
              hours: 2.0,
            ),
          );
        },
        expect: () => [
          isA<BookingFlowState>(),
          isA<BookingFlowState>().having(
            (s) => s.selectedDate,
            'selectedDate',
            isNotNull,
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle concurrent booking attempts',
        build: () {
          when(
            mockCreateBooking.call(any),
          ).thenAnswer((_) async => Left(ServerFailure('Booking conflict')));
          return clientBookingBloc;
        },
        seed: () => BookingFlowState(
          currentStep: BookingStep.paymentMethod,
          selectedService: testService,
          selectedDate: DateTime.now().add(const Duration(days: 1)),
          selectedTimeSlot: '09:00',
          selectedHours: 2.0,
          clientAddress: '123 Main St',
          clientLatitude: 10.8231,
          clientLongitude: 106.6297,
          selectedPartner: testPartner,
          selectedPaymentMethod: PaymentMethod(
            id: 'mock',
            type: PaymentMethodType.mock,
            name: 'mock',
            displayName: 'Mock Payment',
          ),
          totalPrice: 110000.0,
        ),
        act: (bloc) async {
          // Simulate rapid booking attempts
          bloc.add(const CreateBookingEvent('user-1'));
          bloc.add(const CreateBookingEvent('user-1'));
          bloc.add(const CreateBookingEvent('user-1'));
        },
        expect: () => [
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>().having(
            (s) => s.error,
            'error',
            'Booking conflict',
          ),
          // Additional rapid attempts will continue to show loading/error states
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>().having(
            (s) => s.error,
            'error',
            'Booking conflict',
          ),
          isA<BookingFlowState>().having((s) => s.isLoading, 'loading', true),
          isA<BookingFlowState>().having(
            (s) => s.error,
            'error',
            'Booking conflict',
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle state reset during active operations',
        build: () {
          when(mockGetAvailableServices.call(any)).thenAnswer((_) async {
            await Future.delayed(const Duration(milliseconds: 200));
            return Right([testService]);
          });
          return clientBookingBloc;
        },
        act: (bloc) async {
          bloc.add(const LoadAvailableServicesEvent());
          await Future.delayed(const Duration(milliseconds: 100));
          // Reset while loading
          bloc.add(const ResetBookingFlowEvent());
        },
        expect: () => [
          isA<ClientBookingLoading>(),
          isA<ClientBookingInitial>(),
        ],
      );
    });

    group('Data Validation', () {
      blocTest<ClientBookingBloc, ClientBookingState>(
        'should validate required booking fields',
        build: () => clientBookingBloc,
        seed: () => const BookingFlowState(
          currentStep: BookingStep.paymentMethod,
          // Missing required fields to trigger validation error
        ),
        act: (bloc) async {
          // Try to create booking without required fields
          bloc.add(const CreateBookingEvent('user-1'));
        },
        expect: () => [
          isA<BookingFlowState>().having(
            (s) => s.error,
            'error',
            contains('incomplete'),
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should validate location coordinates',
        build: () => clientBookingBloc,
        act: (bloc) async {
          bloc.add(SelectServiceEvent(testService));
          await Future.delayed(const Duration(milliseconds: 50));
          // Invalid coordinates
          bloc.add(
            const SetClientLocationEvent(
              address: '123 Main St',
              latitude: 200.0, // Invalid latitude
              longitude: 300.0, // Invalid longitude
            ),
          );
        },
        expect: () => [
          isA<BookingFlowState>(),
          isA<BookingFlowState>().having(
            (s) => s.clientLatitude,
            'clientLatitude',
            200.0,
          ),
        ],
      );

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should validate time slot availability',
        build: () => clientBookingBloc,
        act: (bloc) async {
          bloc.add(SelectServiceEvent(testService));
          await Future.delayed(const Duration(milliseconds: 50));
          // Invalid time slot
          bloc.add(
            SelectDateTimeEvent(
              date: DateTime.now().add(const Duration(days: 1)),
              timeSlot: '25:00', // Invalid time
              hours: 2.0,
            ),
          );
        },
        expect: () => [
          isA<BookingFlowState>(),
          isA<BookingFlowState>().having(
            (s) => s.selectedTimeSlot,
            'selectedTimeSlot',
            '25:00',
          ),
        ],
      );
    });

    group('Memory Management', () {
      test('should properly dispose resources', () async {
        // Create multiple BLoCs to test memory management
        final blocs = <ClientBookingBloc>[];

        for (int i = 0; i < 10; i++) {
          final bloc = ClientBookingBloc(
            getAvailableServices: mockGetAvailableServices,
            searchAvailablePartners: mockSearchAvailablePartners,
            createBooking: mockCreateBooking,
            processPayment: mockProcessPayment,
            getAvailablePaymentMethods: mockGetAvailablePaymentMethods,
            getClientBookings: mockGetClientBookings,
          );
          blocs.add(bloc);
        }

        // Close all BLoCs
        for (final bloc in blocs) {
          await bloc.close();
        }

        // Verify all BLoCs are closed
        for (final bloc in blocs) {
          expect(bloc.isClosed, isTrue);
        }
      });

      blocTest<ClientBookingBloc, ClientBookingState>(
        'should handle rapid state changes without memory leaks',
        build: () {
          when(
            mockGetAvailableServices.call(any),
          ).thenAnswer((_) async => Right([testService]));
          return clientBookingBloc;
        },
        act: (bloc) async {
          // Rapid fire events
          for (int i = 0; i < 50; i++) {
            bloc.add(const LoadAvailableServicesEvent());
            if (i % 10 == 0) {
              await Future.delayed(const Duration(milliseconds: 10));
            }
          }
        },
        // Remove expect to avoid checking specific state sequences
        // This test focuses on performance and memory management
        verify: (_) {
          // Verify the use case was called appropriately
          verify(mockGetAvailableServices.call(any)).called(greaterThan(1));
        },
      );
    });
  });
}

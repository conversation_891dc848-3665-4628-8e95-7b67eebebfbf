// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/shared/services/notification_action_handler_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;

import 'package:cloud_firestore/cloud_firestore.dart' as _i5;
import 'package:dartz/dartz.dart' as _i3;
import 'package:flutter/widgets.dart' as _i7;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i14;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i2;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i12;
import 'package:flutter_pro_test/shared/models/user_model.dart' as _i4;
import 'package:flutter_pro_test/shared/repositories/base_repository.dart'
    as _i17;
import 'package:flutter_pro_test/shared/repositories/user_repository.dart'
    as _i15;
import 'package:go_router/src/configuration.dart' as _i6;
import 'package:go_router/src/delegate.dart' as _i8;
import 'package:go_router/src/information_provider.dart' as _i9;
import 'package:go_router/src/match.dart' as _i19;
import 'package:go_router/src/parser.dart' as _i10;
import 'package:go_router/src/router.dart' as _i18;
import 'package:go_router/src/state.dart' as _i11;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i16;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthUser_0 extends _i1.SmartFake implements _i2.AuthUser {
  _FakeAuthUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserModel_2 extends _i1.SmartFake implements _i4.UserModel {
  _FakeUserModel_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeQuery_3<T extends Object?> extends _i1.SmartFake
    implements _i5.Query<T> {
  _FakeQuery_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRouteConfiguration_4 extends _i1.SmartFake
    implements _i6.RouteConfiguration {
  _FakeRouteConfiguration_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeBackButtonDispatcher_5 extends _i1.SmartFake
    implements _i7.BackButtonDispatcher {
  _FakeBackButtonDispatcher_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouterDelegate_6 extends _i1.SmartFake
    implements _i8.GoRouterDelegate {
  _FakeGoRouterDelegate_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouteInformationProvider_7 extends _i1.SmartFake
    implements _i9.GoRouteInformationProvider {
  _FakeGoRouteInformationProvider_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouteInformationParser_8 extends _i1.SmartFake
    implements _i10.GoRouteInformationParser {
  _FakeGoRouteInformationParser_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoRouterState_9 extends _i1.SmartFake implements _i11.GoRouterState {
  _FakeGoRouterState_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i12.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i13.Stream<_i2.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i13.Stream<_i2.AuthUser>.empty(),
          )
          as _i13.Stream<_i2.AuthUser>);

  @override
  _i2.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_0(this, Invocation.getter(#currentUser)),
          )
          as _i2.AuthUser);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>
  signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i14.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>
  signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i14.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, String>>.value(
              _FakeEither_1<_i14.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, String>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i14.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, bool>>.value(
              _FakeEither_1<_i14.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, bool>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i14.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i2.AuthUser>>);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i15.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get collectionName =>
      (super.noSuchMethod(
            Invocation.getter(#collectionName),
            returnValue: _i16.dummyValue<String>(
              this,
              Invocation.getter(#collectionName),
            ),
          )
          as String);

  @override
  _i4.UserModel fromFirestore(_i5.DocumentSnapshot<Object?>? doc) =>
      (super.noSuchMethod(
            Invocation.method(#fromFirestore, [doc]),
            returnValue: _FakeUserModel_2(
              this,
              Invocation.method(#fromFirestore, [doc]),
            ),
          )
          as _i4.UserModel);

  @override
  Map<String, dynamic> toMap(_i4.UserModel? model) =>
      (super.noSuchMethod(
            Invocation.method(#toMap, [model]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>> createUser(
    _i4.UserModel? user,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createUser, [user]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#createUser, [user]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getCurrentUser, []),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>> updateUser(
    _i4.UserModel? user,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUser, [user]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#updateUser, [user]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>> getUserByEmail(
    String? email,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUserByEmail, [email]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getUserByEmail, [email]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>> getUserByPhone(
    String? phone,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUserByPhone, [phone]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel?>(
                    this,
                    Invocation.method(#getUserByPhone, [phone]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel?>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>> getUsersByRole(
    String? role,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUsersByRole, [role]),
            returnValue:
                _i13.Future<
                  _i3.Either<_i14.Failure, List<_i4.UserModel>>
                >.value(
                  _FakeEither_1<_i14.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(#getUsersByRole, [role]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> updateFCMToken(
    String? userId,
    String? token,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#updateFCMToken, [userId, token]),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> updateLastSeen(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#updateLastSeen, [userId]),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#updateLastSeen, [userId]),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel?>> listenToCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#listenToCurrentUser, []),
            returnValue:
                _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel?>>.empty(),
          )
          as _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel?>>);

  @override
  _i13.Stream<_i3.Either<_i14.Failure, List<_i4.UserModel>>>
  listenToUsersByRole(String? role) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUsersByRole, [role]),
            returnValue:
                _i13.Stream<
                  _i3.Either<_i14.Failure, List<_i4.UserModel>>
                >.empty(),
          )
          as _i13.Stream<_i3.Either<_i14.Failure, List<_i4.UserModel>>>);

  @override
  bool isValidEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#isValidEmail, [email]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidPhone(String? phone) =>
      (super.noSuchMethod(
            Invocation.method(#isValidPhone, [phone]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidName(String? name) =>
      (super.noSuchMethod(
            Invocation.method(#isValidName, [name]),
            returnValue: false,
          )
          as bool);

  @override
  _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>> searchUsers(
    String? searchTerm, {
    String? role,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #searchUsers,
              [searchTerm],
              {#role: role, #limit: limit},
            ),
            returnValue:
                _i13.Future<
                  _i3.Either<_i14.Failure, List<_i4.UserModel>>
                >.value(
                  _FakeEither_1<_i14.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(
                      #searchUsers,
                      [searchTerm],
                      {#role: role, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>> create(
    _i4.UserModel? model,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#create, [model]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#create, [model]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>> getById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getById, [id]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#getById, [id]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>> getAll({
    _i5.Query<Map<String, dynamic>>? query,
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAll, [], {#query: query, #limit: limit}),
            returnValue:
                _i13.Future<
                  _i3.Either<_i14.Failure, List<_i4.UserModel>>
                >.value(
                  _FakeEither_1<_i14.Failure, List<_i4.UserModel>>(
                    this,
                    Invocation.method(#getAll, [], {
                      #query: query,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, List<_i4.UserModel>>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>> update(
    String? id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#update, [id, data]),
            returnValue:
                _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>.value(
                  _FakeEither_1<_i14.Failure, _i4.UserModel>(
                    this,
                    Invocation.method(#update, [id, data]),
                  ),
                ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> delete(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [id]),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#delete, [id]),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel>> listenToDocument(
    String? id,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToDocument, [id]),
            returnValue:
                _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel>>.empty(),
          )
          as _i13.Stream<_i3.Either<_i14.Failure, _i4.UserModel>>);

  @override
  _i13.Stream<_i3.Either<_i14.Failure, List<_i4.UserModel>>>
  listenToCollection({_i5.Query<Map<String, dynamic>>? query, int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(#listenToCollection, [], {
              #query: query,
              #limit: limit,
            }),
            returnValue:
                _i13.Stream<
                  _i3.Either<_i14.Failure, List<_i4.UserModel>>
                >.empty(),
          )
          as _i13.Stream<_i3.Either<_i14.Failure, List<_i4.UserModel>>>);

  @override
  _i13.Future<_i3.Either<_i14.Failure, void>> batchWrite(
    List<_i17.BatchOperation<_i4.UserModel>>? operations,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#batchWrite, [operations]),
            returnValue: _i13.Future<_i3.Either<_i14.Failure, void>>.value(
              _FakeEither_1<_i14.Failure, void>(
                this,
                Invocation.method(#batchWrite, [operations]),
              ),
            ),
          )
          as _i13.Future<_i3.Either<_i14.Failure, void>>);

  @override
  _i5.Query<Map<String, dynamic>> where(
    String? field,
    dynamic isEqualTo, {
    dynamic isNotEqualTo,
    dynamic isLessThan,
    dynamic isLessThanOrEqualTo,
    dynamic isGreaterThan,
    dynamic isGreaterThanOrEqualTo,
    dynamic arrayContains,
    List<dynamic>? arrayContainsAny,
    List<dynamic>? whereIn,
    List<dynamic>? whereNotIn,
    bool? isNull,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #where,
              [field, isEqualTo],
              {
                #isNotEqualTo: isNotEqualTo,
                #isLessThan: isLessThan,
                #isLessThanOrEqualTo: isLessThanOrEqualTo,
                #isGreaterThan: isGreaterThan,
                #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                #arrayContains: arrayContains,
                #arrayContainsAny: arrayContainsAny,
                #whereIn: whereIn,
                #whereNotIn: whereNotIn,
                #isNull: isNull,
              },
            ),
            returnValue: _FakeQuery_3<Map<String, dynamic>>(
              this,
              Invocation.method(
                #where,
                [field, isEqualTo],
                {
                  #isNotEqualTo: isNotEqualTo,
                  #isLessThan: isLessThan,
                  #isLessThanOrEqualTo: isLessThanOrEqualTo,
                  #isGreaterThan: isGreaterThan,
                  #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
                  #arrayContains: arrayContains,
                  #arrayContainsAny: arrayContainsAny,
                  #whereIn: whereIn,
                  #whereNotIn: whereNotIn,
                  #isNull: isNull,
                },
              ),
            ),
          )
          as _i5.Query<Map<String, dynamic>>);

  @override
  _i5.Query<Map<String, dynamic>> orderBy(
    String? field, {
    bool? descending = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#orderBy, [field], {#descending: descending}),
            returnValue: _FakeQuery_3<Map<String, dynamic>>(
              this,
              Invocation.method(#orderBy, [field], {#descending: descending}),
            ),
          )
          as _i5.Query<Map<String, dynamic>>);
}

/// A class which mocks [GoRouter].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoRouter extends _i1.Mock implements _i18.GoRouter {
  MockGoRouter() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.RouteConfiguration get configuration =>
      (super.noSuchMethod(
            Invocation.getter(#configuration),
            returnValue: _FakeRouteConfiguration_4(
              this,
              Invocation.getter(#configuration),
            ),
          )
          as _i6.RouteConfiguration);

  @override
  _i7.BackButtonDispatcher get backButtonDispatcher =>
      (super.noSuchMethod(
            Invocation.getter(#backButtonDispatcher),
            returnValue: _FakeBackButtonDispatcher_5(
              this,
              Invocation.getter(#backButtonDispatcher),
            ),
          )
          as _i7.BackButtonDispatcher);

  @override
  _i8.GoRouterDelegate get routerDelegate =>
      (super.noSuchMethod(
            Invocation.getter(#routerDelegate),
            returnValue: _FakeGoRouterDelegate_6(
              this,
              Invocation.getter(#routerDelegate),
            ),
          )
          as _i8.GoRouterDelegate);

  @override
  _i9.GoRouteInformationProvider get routeInformationProvider =>
      (super.noSuchMethod(
            Invocation.getter(#routeInformationProvider),
            returnValue: _FakeGoRouteInformationProvider_7(
              this,
              Invocation.getter(#routeInformationProvider),
            ),
          )
          as _i9.GoRouteInformationProvider);

  @override
  _i10.GoRouteInformationParser get routeInformationParser =>
      (super.noSuchMethod(
            Invocation.getter(#routeInformationParser),
            returnValue: _FakeGoRouteInformationParser_8(
              this,
              Invocation.getter(#routeInformationParser),
            ),
          )
          as _i10.GoRouteInformationParser);

  @override
  bool get overridePlatformDefaultLocation =>
      (super.noSuchMethod(
            Invocation.getter(#overridePlatformDefaultLocation),
            returnValue: false,
          )
          as bool);

  @override
  _i11.GoRouterState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeGoRouterState_9(this, Invocation.getter(#state)),
          )
          as _i11.GoRouterState);

  @override
  set configuration(_i6.RouteConfiguration? _configuration) =>
      super.noSuchMethod(
        Invocation.setter(#configuration, _configuration),
        returnValueForMissingStub: null,
      );

  @override
  set routerDelegate(_i8.GoRouterDelegate? _routerDelegate) =>
      super.noSuchMethod(
        Invocation.setter(#routerDelegate, _routerDelegate),
        returnValueForMissingStub: null,
      );

  @override
  set routeInformationProvider(
    _i9.GoRouteInformationProvider? _routeInformationProvider,
  ) => super.noSuchMethod(
    Invocation.setter(#routeInformationProvider, _routeInformationProvider),
    returnValueForMissingStub: null,
  );

  @override
  set routeInformationParser(
    _i10.GoRouteInformationParser? _routeInformationParser,
  ) => super.noSuchMethod(
    Invocation.setter(#routeInformationParser, _routeInformationParser),
    returnValueForMissingStub: null,
  );

  @override
  bool canPop() =>
      (super.noSuchMethod(Invocation.method(#canPop, []), returnValue: false)
          as bool);

  @override
  String namedLocation(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    String? fragment,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #namedLocation,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #fragment: fragment,
              },
            ),
            returnValue: _i16.dummyValue<String>(
              this,
              Invocation.method(
                #namedLocation,
                [name],
                {
                  #pathParameters: pathParameters,
                  #queryParameters: queryParameters,
                  #fragment: fragment,
                },
              ),
            ),
          )
          as String);

  @override
  void go(String? location, {Object? extra}) => super.noSuchMethod(
    Invocation.method(#go, [location], {#extra: extra}),
    returnValueForMissingStub: null,
  );

  @override
  void restore(_i19.RouteMatchList? matchList) => super.noSuchMethod(
    Invocation.method(#restore, [matchList]),
    returnValueForMissingStub: null,
  );

  @override
  void goNamed(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
    String? fragment,
  }) => super.noSuchMethod(
    Invocation.method(
      #goNamed,
      [name],
      {
        #pathParameters: pathParameters,
        #queryParameters: queryParameters,
        #extra: extra,
        #fragment: fragment,
      },
    ),
    returnValueForMissingStub: null,
  );

  @override
  _i13.Future<T?> push<T extends Object?>(String? location, {Object? extra}) =>
      (super.noSuchMethod(
            Invocation.method(#push, [location], {#extra: extra}),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<T?> pushNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #pushNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<T?> pushReplacement<T extends Object?>(
    String? location, {
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#pushReplacement, [location], {#extra: extra}),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<T?> pushReplacementNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #pushReplacementNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<T?> replace<T>(String? location, {Object? extra}) =>
      (super.noSuchMethod(
            Invocation.method(#replace, [location], {#extra: extra}),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  _i13.Future<T?> replaceNamed<T>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #replaceNamed,
              [name],
              {
                #pathParameters: pathParameters,
                #queryParameters: queryParameters,
                #extra: extra,
              },
            ),
            returnValue: _i13.Future<T?>.value(),
          )
          as _i13.Future<T?>);

  @override
  void pop<T extends Object?>([T? result]) => super.noSuchMethod(
    Invocation.method(#pop, [result]),
    returnValueForMissingStub: null,
  );

  @override
  void refresh() => super.noSuchMethod(
    Invocation.method(#refresh, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

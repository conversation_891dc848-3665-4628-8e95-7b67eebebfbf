// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/admin/presentation/bloc/admin_dashboard_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i7;
import 'package:flutter_pro_test/features/admin/domain/entities/admin_user.dart'
    as _i3;
import 'package:flutter_pro_test/features/admin/domain/entities/booking_analytics.dart'
    as _i9;
import 'package:flutter_pro_test/features/admin/domain/entities/partner_analytics.dart'
    as _i10;
import 'package:flutter_pro_test/features/admin/domain/entities/report_config.dart'
    as _i13;
import 'package:flutter_pro_test/features/admin/domain/entities/revenue_analytics.dart'
    as _i12;
import 'package:flutter_pro_test/features/admin/domain/entities/system_metrics.dart'
    as _i8;
import 'package:flutter_pro_test/features/admin/domain/entities/user_analytics.dart'
    as _i11;
import 'package:flutter_pro_test/features/admin/domain/repositories/admin_repository.dart'
    as _i5;
import 'package:flutter_pro_test/features/admin/domain/repositories/analytics_repository.dart'
    as _i4;
import 'package:flutter_pro_test/features/admin/domain/usecases/get_booking_analytics.dart'
    as _i15;
import 'package:flutter_pro_test/features/admin/domain/usecases/get_system_metrics.dart'
    as _i14;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAdminUser_1 extends _i1.SmartFake implements _i3.AdminUser {
  _FakeAdminUser_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAnalyticsRepository_2 extends _i1.SmartFake
    implements _i4.AnalyticsRepository {
  _FakeAnalyticsRepository_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAdminRepository_3 extends _i1.SmartFake
    implements _i5.AdminRepository {
  _FakeAdminRepository_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AnalyticsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsRepository extends _i1.Mock
    implements _i4.AnalyticsRepository {
  MockAnalyticsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>> getSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemMetrics, []),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>>.value(
                  _FakeEither_0<_i7.Failure, _i8.SystemMetrics>(
                    this,
                    Invocation.method(#getSystemMetrics, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>>);

  @override
  _i6.Stream<_i2.Either<_i7.Failure, _i8.SystemMetrics>> watchSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemMetrics, []),
            returnValue:
                _i6.Stream<_i2.Either<_i7.Failure, _i8.SystemMetrics>>.empty(),
          )
          as _i6.Stream<_i2.Either<_i7.Failure, _i8.SystemMetrics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>>
  getBookingAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
    String? partnerId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
              #partnerId: partnerId,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>>.value(
                  _FakeEither_0<_i7.Failure, _i9.BookingAnalytics>(
                    this,
                    Invocation.method(#getBookingAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #serviceId: serviceId,
                      #partnerId: partnerId,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i10.PartnerAnalytics>>
  getPartnerAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includePerformanceDetails = false,
    bool? includeQualityMetrics = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includePerformanceDetails: includePerformanceDetails,
              #includeQualityMetrics: includeQualityMetrics,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, _i10.PartnerAnalytics>
                >.value(
                  _FakeEither_0<_i7.Failure, _i10.PartnerAnalytics>(
                    this,
                    Invocation.method(#getPartnerAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includePerformanceDetails: includePerformanceDetails,
                      #includeQualityMetrics: includeQualityMetrics,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i10.PartnerAnalytics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i11.UserAnalytics>> getUserAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeCohortAnalysis = false,
    bool? includeSegmentation = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeCohortAnalysis: includeCohortAnalysis,
              #includeSegmentation: includeSegmentation,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i11.UserAnalytics>>.value(
                  _FakeEither_0<_i7.Failure, _i11.UserAnalytics>(
                    this,
                    Invocation.method(#getUserAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeCohortAnalysis: includeCohortAnalysis,
                      #includeSegmentation: includeSegmentation,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i11.UserAnalytics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i12.RevenueAnalytics>>
  getRevenueAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeForecasts = false,
    bool? includeComparisons = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRevenueAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeForecasts: includeForecasts,
              #includeComparisons: includeComparisons,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, _i12.RevenueAnalytics>
                >.value(
                  _FakeEither_0<_i7.Failure, _i12.RevenueAnalytics>(
                    this,
                    Invocation.method(#getRevenueAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeForecasts: includeForecasts,
                      #includeComparisons: includeComparisons,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i12.RevenueAnalytics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i4.SystemHealth>> getSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemHealth, []),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i4.SystemHealth>>.value(
                  _FakeEither_0<_i7.Failure, _i4.SystemHealth>(
                    this,
                    Invocation.method(#getSystemHealth, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i4.SystemHealth>>);

  @override
  _i6.Stream<_i2.Either<_i7.Failure, _i4.SystemHealth>> watchSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemHealth, []),
            returnValue:
                _i6.Stream<_i2.Either<_i7.Failure, _i4.SystemHealth>>.empty(),
          )
          as _i6.Stream<_i2.Either<_i7.Failure, _i4.SystemHealth>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, String>> exportAnalyticsData({
    required _i4.AnalyticsExportType? type,
    required DateTime? startDate,
    required DateTime? endDate,
    required _i4.AnalyticsExportFormat? format,
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#exportAnalyticsData, [], {
              #type: type,
              #startDate: startDate,
              #endDate: endDate,
              #format: format,
              #filters: filters,
            }),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, String>>.value(
              _FakeEither_0<_i7.Failure, String>(
                this,
                Invocation.method(#exportAnalyticsData, [], {
                  #type: type,
                  #startDate: startDate,
                  #endDate: endDate,
                  #format: format,
                  #filters: filters,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, String>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i4.AnalyticsSummary>>
  getAnalyticsSummary({
    required DateTime? startDate,
    required DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsSummary, [], {
              #startDate: startDate,
              #endDate: endDate,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i4.AnalyticsSummary>>.value(
                  _FakeEither_0<_i7.Failure, _i4.AnalyticsSummary>(
                    this,
                    Invocation.method(#getAnalyticsSummary, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i4.AnalyticsSummary>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i4.ComparativeAnalytics>>
  getComparativeAnalytics({
    required DateTime? currentStart,
    required DateTime? currentEnd,
    required DateTime? previousStart,
    required DateTime? previousEnd,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getComparativeAnalytics, [], {
              #currentStart: currentStart,
              #currentEnd: currentEnd,
              #previousStart: previousStart,
              #previousEnd: previousEnd,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, _i4.ComparativeAnalytics>
                >.value(
                  _FakeEither_0<_i7.Failure, _i4.ComparativeAnalytics>(
                    this,
                    Invocation.method(#getComparativeAnalytics, [], {
                      #currentStart: currentStart,
                      #currentEnd: currentEnd,
                      #previousStart: previousStart,
                      #previousEnd: previousEnd,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i4.ComparativeAnalytics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i4.TopPerformingMetrics>>
  getTopPerformingMetrics({
    required DateTime? startDate,
    required DateTime? endDate,
    int? limit = 10,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTopPerformingMetrics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #limit: limit,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, _i4.TopPerformingMetrics>
                >.value(
                  _FakeEither_0<_i7.Failure, _i4.TopPerformingMetrics>(
                    this,
                    Invocation.method(#getTopPerformingMetrics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i4.TopPerformingMetrics>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, List<_i4.AnalyticsAlert>>>
  getAnalyticsAlerts({
    _i4.AnalyticsAlertSeverity? severity,
    bool? unreadOnly = false,
    int? limit = 50,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsAlerts, [], {
              #severity: severity,
              #unreadOnly: unreadOnly,
              #limit: limit,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, List<_i4.AnalyticsAlert>>
                >.value(
                  _FakeEither_0<_i7.Failure, List<_i4.AnalyticsAlert>>(
                    this,
                    Invocation.method(#getAnalyticsAlerts, [], {
                      #severity: severity,
                      #unreadOnly: unreadOnly,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, List<_i4.AnalyticsAlert>>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> markAlertAsRead(String? alertId) =>
      (super.noSuchMethod(
            Invocation.method(#markAlertAsRead, [alertId]),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#markAlertAsRead, [alertId]),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, Map<String, dynamic>>> executeCustomQuery({
    required String? query,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeCustomQuery, [], {
              #query: query,
              #parameters: parameters,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i7.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#executeCustomQuery, [], {
                      #query: query,
                      #parameters: parameters,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, Map<String, dynamic>>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i13.GeneratedReport>> generateReport({
    required _i13.ReportConfig? config,
    Map<String, dynamic>? customData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateReport, [], {
              #config: config,
              #customData: customData,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i13.GeneratedReport>>.value(
                  _FakeEither_0<_i7.Failure, _i13.GeneratedReport>(
                    this,
                    Invocation.method(#generateReport, [], {
                      #config: config,
                      #customData: customData,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i13.GeneratedReport>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, List<_i13.ReportConfig>>>
  getReportConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getReportConfigs, []),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, List<_i13.ReportConfig>>
                >.value(
                  _FakeEither_0<_i7.Failure, List<_i13.ReportConfig>>(
                    this,
                    Invocation.method(#getReportConfigs, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, List<_i13.ReportConfig>>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>> createReportConfig({
    required String? name,
    required String? description,
    required _i13.ReportType? type,
    required _i13.ReportFormat? format,
    required DateTime? startDate,
    required DateTime? endDate,
    required List<String>? metrics,
    required List<String>? dimensions,
    List<_i13.ReportFilter>? filters = const [],
    _i13.ReportSchedule? schedule,
    List<String>? recipients = const [],
    Map<String, dynamic>? customSettings = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createReportConfig, [], {
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>>.value(
                  _FakeEither_0<_i7.Failure, _i13.ReportConfig>(
                    this,
                    Invocation.method(#createReportConfig, [], {
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>> updateReportConfig({
    required String? id,
    String? name,
    String? description,
    _i13.ReportType? type,
    _i13.ReportFormat? format,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? metrics,
    List<String>? dimensions,
    List<_i13.ReportFilter>? filters,
    _i13.ReportSchedule? schedule,
    List<String>? recipients,
    Map<String, dynamic>? customSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateReportConfig, [], {
              #id: id,
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>>.value(
                  _FakeEither_0<_i7.Failure, _i13.ReportConfig>(
                    this,
                    Invocation.method(#updateReportConfig, [], {
                      #id: id,
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i13.ReportConfig>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> deleteReportConfig(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteReportConfig, [id]),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#deleteReportConfig, [id]),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, List<_i13.GeneratedReport>>>
  getGeneratedReports({String? configId, int? limit = 20, int? offset = 0}) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneratedReports, [], {
              #configId: configId,
              #limit: limit,
              #offset: offset,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, List<_i13.GeneratedReport>>
                >.value(
                  _FakeEither_0<_i7.Failure, List<_i13.GeneratedReport>>(
                    this,
                    Invocation.method(#getGeneratedReports, [], {
                      #configId: configId,
                      #limit: limit,
                      #offset: offset,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, List<_i13.GeneratedReport>>>);
}

/// A class which mocks [AdminRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminRepository extends _i1.Mock implements _i5.AdminRepository {
  MockAdminRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Stream<_i3.AdminUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i6.Stream<_i3.AdminUser>.empty(),
          )
          as _i6.Stream<_i3.AdminUser>);

  @override
  _i3.AdminUser get currentAdmin =>
      (super.noSuchMethod(
            Invocation.getter(#currentAdmin),
            returnValue: _FakeAdminUser_1(
              this,
              Invocation.getter(#currentAdmin),
            ),
          )
          as _i3.AdminUser);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>> authenticateAdmin({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#authenticateAdmin, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>.value(
                  _FakeEither_0<_i7.Failure, _i3.AdminUser>(
                    this,
                    Invocation.method(#authenticateAdmin, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>> getAdminById(
    String? adminId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAdminById, [adminId]),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>.value(
                  _FakeEither_0<_i7.Failure, _i3.AdminUser>(
                    this,
                    Invocation.method(#getAdminById, [adminId]),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, List<_i3.AdminUser>>> getAllAdmins() =>
      (super.noSuchMethod(
            Invocation.method(#getAllAdmins, []),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, List<_i3.AdminUser>>>.value(
                  _FakeEither_0<_i7.Failure, List<_i3.AdminUser>>(
                    this,
                    Invocation.method(#getAllAdmins, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, List<_i3.AdminUser>>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>> createAdmin({
    required String? email,
    required String? displayName,
    required _i3.AdminRole? role,
    List<_i3.AdminPermission>? customPermissions,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createAdmin, [], {
              #email: email,
              #displayName: displayName,
              #role: role,
              #customPermissions: customPermissions,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>.value(
                  _FakeEither_0<_i7.Failure, _i3.AdminUser>(
                    this,
                    Invocation.method(#createAdmin, [], {
                      #email: email,
                      #displayName: displayName,
                      #role: role,
                      #customPermissions: customPermissions,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>> updateAdmin({
    required String? adminId,
    String? displayName,
    _i3.AdminRole? role,
    List<_i3.AdminPermission>? permissions,
    bool? isActive,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateAdmin, [], {
              #adminId: adminId,
              #displayName: displayName,
              #role: role,
              #permissions: permissions,
              #isActive: isActive,
            }),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>.value(
                  _FakeEither_0<_i7.Failure, _i3.AdminUser>(
                    this,
                    Invocation.method(#updateAdmin, [], {
                      #adminId: adminId,
                      #displayName: displayName,
                      #role: role,
                      #permissions: permissions,
                      #isActive: isActive,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> deleteAdmin(String? adminId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAdmin, [adminId]),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#deleteAdmin, [adminId]),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> updateLastLogin(String? adminId) =>
      (super.noSuchMethod(
            Invocation.method(#updateLastLogin, [adminId]),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#updateLastLogin, [adminId]),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, bool>> hasPermission({
    required String? adminId,
    required _i3.AdminPermission? permission,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#hasPermission, [], {
              #adminId: adminId,
              #permission: permission,
            }),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, bool>>.value(
              _FakeEither_0<_i7.Failure, bool>(
                this,
                Invocation.method(#hasPermission, [], {
                  #adminId: adminId,
                  #permission: permission,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, bool>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, bool>> validateSession() =>
      (super.noSuchMethod(
            Invocation.method(#validateSession, []),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, bool>>.value(
              _FakeEither_0<_i7.Failure, bool>(
                this,
                Invocation.method(#validateSession, []),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, bool>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>> refreshAdmin() =>
      (super.noSuchMethod(
            Invocation.method(#refreshAdmin, []),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>.value(
                  _FakeEither_0<_i7.Failure, _i3.AdminUser>(
                    this,
                    Invocation.method(#refreshAdmin, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i3.AdminUser>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, List<_i5.AdminActivityLog>>>
  getAdminActivityLogs({
    String? adminId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 50,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAdminActivityLogs, [], {
              #adminId: adminId,
              #startDate: startDate,
              #endDate: endDate,
              #limit: limit,
            }),
            returnValue:
                _i6.Future<
                  _i2.Either<_i7.Failure, List<_i5.AdminActivityLog>>
                >.value(
                  _FakeEither_0<_i7.Failure, List<_i5.AdminActivityLog>>(
                    this,
                    Invocation.method(#getAdminActivityLogs, [], {
                      #adminId: adminId,
                      #startDate: startDate,
                      #endDate: endDate,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, List<_i5.AdminActivityLog>>>);

  @override
  _i6.Future<_i2.Either<_i7.Failure, void>> logAdminActivity({
    required String? adminId,
    required _i5.AdminActivityType? activityType,
    required String? description,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logAdminActivity, [], {
              #adminId: adminId,
              #activityType: activityType,
              #description: description,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<_i2.Either<_i7.Failure, void>>.value(
              _FakeEither_0<_i7.Failure, void>(
                this,
                Invocation.method(#logAdminActivity, [], {
                  #adminId: adminId,
                  #activityType: activityType,
                  #description: description,
                  #metadata: metadata,
                }),
              ),
            ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, void>>);
}

/// A class which mocks [GetSystemMetrics].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetSystemMetrics extends _i1.Mock implements _i14.GetSystemMetrics {
  MockGetSystemMetrics() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.AnalyticsRepository get analyticsRepository =>
      (super.noSuchMethod(
            Invocation.getter(#analyticsRepository),
            returnValue: _FakeAnalyticsRepository_2(
              this,
              Invocation.getter(#analyticsRepository),
            ),
          )
          as _i4.AnalyticsRepository);

  @override
  _i5.AdminRepository get adminRepository =>
      (super.noSuchMethod(
            Invocation.getter(#adminRepository),
            returnValue: _FakeAdminRepository_3(
              this,
              Invocation.getter(#adminRepository),
            ),
          )
          as _i5.AdminRepository);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>> call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>>.value(
                  _FakeEither_0<_i7.Failure, _i8.SystemMetrics>(
                    this,
                    Invocation.method(#call, []),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i8.SystemMetrics>>);
}

/// A class which mocks [GetBookingAnalytics].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetBookingAnalytics extends _i1.Mock
    implements _i15.GetBookingAnalytics {
  MockGetBookingAnalytics() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.AnalyticsRepository get analyticsRepository =>
      (super.noSuchMethod(
            Invocation.getter(#analyticsRepository),
            returnValue: _FakeAnalyticsRepository_2(
              this,
              Invocation.getter(#analyticsRepository),
            ),
          )
          as _i4.AnalyticsRepository);

  @override
  _i5.AdminRepository get adminRepository =>
      (super.noSuchMethod(
            Invocation.getter(#adminRepository),
            returnValue: _FakeAdminRepository_3(
              this,
              Invocation.getter(#adminRepository),
            ),
          )
          as _i5.AdminRepository);

  @override
  _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>> call(
    _i15.GetBookingAnalyticsParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>>.value(
                  _FakeEither_0<_i7.Failure, _i9.BookingAnalytics>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i2.Either<_i7.Failure, _i9.BookingAnalytics>>);
}

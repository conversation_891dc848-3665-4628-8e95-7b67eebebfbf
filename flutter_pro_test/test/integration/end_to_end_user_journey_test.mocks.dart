// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/integration/end_to_end_user_journey_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:dartz/dartz.dart' as _i3;
import 'package:firebase_analytics/firebase_analytics.dart' as _i4;
import 'package:firebase_performance/firebase_performance.dart' as _i5;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i19;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i8;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i18;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i2;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i6;
import 'package:flutter_pro_test/features/booking/domain/entities/booking.dart'
    as _i10;
import 'package:flutter_pro_test/features/booking/domain/entities/booking_request.dart'
    as _i11;
import 'package:flutter_pro_test/features/booking/domain/repositories/booking_repository.dart'
    as _i9;
import 'package:flutter_pro_test/features/client/domain/entities/payment_request.dart'
    as _i13;
import 'package:flutter_pro_test/features/client/domain/entities/payment_result.dart'
    as _i14;
import 'package:flutter_pro_test/features/client/domain/repositories/payment_repository.dart'
    as _i12;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i24;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i22;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i21;
import 'package:flutter_pro_test/features/partner/domain/entities/job.dart'
    as _i16;
import 'package:flutter_pro_test/features/partner/domain/entities/partner_earnings.dart'
    as _i17;
import 'package:flutter_pro_test/features/partner/domain/repositories/partner_job_repository.dart'
    as _i15;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i23;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i20;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthUser_0 extends _i1.SmartFake implements _i2.AuthUser {
  _FakeAuthUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseAnalytics_2 extends _i1.SmartFake
    implements _i4.FirebaseAnalytics {
  _FakeFirebaseAnalytics_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_3 extends _i1.SmartFake
    implements _i5.FirebasePerformance {
  _FakeFirebasePerformance_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i6.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Stream<_i2.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i7.Stream<_i2.AuthUser>.empty(),
          )
          as _i7.Stream<_i2.AuthUser>);

  @override
  _i2.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_0(this, Invocation.getter(#currentUser)),
          )
          as _i2.AuthUser);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, String>>.value(
              _FakeEither_1<_i8.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, String>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, bool>>.value(
              _FakeEither_1<_i8.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, bool>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i8.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i2.AuthUser>>);
}

/// A class which mocks [BookingRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookingRepository extends _i1.Mock implements _i9.BookingRepository {
  MockBookingRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> createBooking(
    _i11.BookingRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createBooking, [request]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#createBooking, [request]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> getBookingById(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingById, [bookingId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#getBookingById, [bookingId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>> getUserBookings(
    String? userId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getUserBookings,
                      [userId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>> getPartnerBookings(
    String? partnerId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getPartnerBookings,
                      [partnerId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>
  getBookingsByDateRange(
    String? userId,
    DateTime? startDate,
    DateTime? endDate, {
    bool? isPartner = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getBookingsByDateRange,
              [userId, startDate, endDate],
              {#isPartner: isPartner},
            ),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getBookingsByDateRange,
                      [userId, startDate, endDate],
                      {#isPartner: isPartner},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i10.Booking>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> updateBookingStatus(
    String? bookingId,
    _i10.BookingStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateBookingStatus, [bookingId, status]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#updateBookingStatus, [
                      bookingId,
                      status,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> cancelBooking(
    String? bookingId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelBooking, [bookingId, cancellationReason]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#cancelBooking, [
                      bookingId,
                      cancellationReason,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> confirmBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#confirmBooking, [bookingId, partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#confirmBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> startBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startBooking, [bookingId, partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#startBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>> completeBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeBooking, [bookingId, partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>.value(
                  _FakeEither_1<_i8.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#completeBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i10.Booking>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>> listenToUserBookings(
    String? userId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>>
  listenToPartnerBookings(
    String? partnerId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, List<_i10.Booking>>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, _i10.Booking>> listenToBooking(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToBooking, [bookingId]),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, _i10.Booking>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, _i10.Booking>>);
}

/// A class which mocks [PaymentRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPaymentRepository extends _i1.Mock implements _i12.PaymentRepository {
  MockPaymentRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i13.PaymentMethod>>>
  getAvailablePaymentMethods() =>
      (super.noSuchMethod(
            Invocation.method(#getAvailablePaymentMethods, []),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, List<_i13.PaymentMethod>>
                >.value(
                  _FakeEither_1<_i8.Failure, List<_i13.PaymentMethod>>(
                    this,
                    Invocation.method(#getAvailablePaymentMethods, []),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i13.PaymentMethod>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>> processPayment(
    _i13.PaymentRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#processPayment, [request]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>.value(
                  _FakeEither_1<_i8.Failure, _i14.PaymentResult>(
                    this,
                    Invocation.method(#processPayment, [request]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>> verifyPayment(
    String? transactionId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPayment, [transactionId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>.value(
                  _FakeEither_1<_i8.Failure, _i14.PaymentResult>(
                    this,
                    Invocation.method(#verifyPayment, [transactionId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>> refundPayment({
    required String? transactionId,
    required double? amount,
    String? reason,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#refundPayment, [], {
              #transactionId: transactionId,
              #amount: amount,
              #reason: reason,
            }),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>.value(
                  _FakeEither_1<_i8.Failure, _i14.PaymentResult>(
                    this,
                    Invocation.method(#refundPayment, [], {
                      #transactionId: transactionId,
                      #amount: amount,
                      #reason: reason,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i14.PaymentResult>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i14.PaymentResult>>>
  getPaymentHistory({
    required String? userId,
    int? limit = 20,
    String? startAfter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPaymentHistory, [], {
              #userId: userId,
              #limit: limit,
              #startAfter: startAfter,
            }),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, List<_i14.PaymentResult>>
                >.value(
                  _FakeEither_1<_i8.Failure, List<_i14.PaymentResult>>(
                    this,
                    Invocation.method(#getPaymentHistory, [], {
                      #userId: userId,
                      #limit: limit,
                      #startAfter: startAfter,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i14.PaymentResult>>>);
}

/// A class which mocks [PartnerJobRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerJobRepository extends _i1.Mock
    implements _i15.PartnerJobRepository {
  MockPartnerJobRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>> getPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPendingJobs, [partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i16.Job>>(
                    this,
                    Invocation.method(#getPendingJobs, [partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>> getAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAcceptedJobs, [partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i16.Job>>(
                    this,
                    Invocation.method(#getAcceptedJobs, [partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>> getJobHistory(
    String? partnerId, {
    _i16.JobStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobHistory,
              [partnerId],
              {
                #status: status,
                #startDate: startDate,
                #endDate: endDate,
                #limit: limit,
              },
            ),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>.value(
                  _FakeEither_1<_i8.Failure, List<_i16.Job>>(
                    this,
                    Invocation.method(
                      #getJobHistory,
                      [partnerId],
                      {
                        #status: status,
                        #startDate: startDate,
                        #endDate: endDate,
                        #limit: limit,
                      },
                    ),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> getJobById(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [jobId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#getJobById, [jobId]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> acceptJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#acceptJob, [jobId, partnerId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#acceptJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> rejectJob(
    String? jobId,
    String? partnerId,
    String? rejectionReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rejectJob, [jobId, partnerId, rejectionReason]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#rejectJob, [
                  jobId,
                  partnerId,
                  rejectionReason,
                ]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> startJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startJob, [jobId, partnerId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#startJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> completeJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeJob, [jobId, partnerId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#completeJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i16.Job>> cancelJob(
    String? jobId,
    String? partnerId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelJob, [
              jobId,
              partnerId,
              cancellationReason,
            ]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>.value(
              _FakeEither_1<_i8.Failure, _i16.Job>(
                this,
                Invocation.method(#cancelJob, [
                  jobId,
                  partnerId,
                  cancellationReason,
                ]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>> listenToPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToPendingJobs, [partnerId]),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>> listenToAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToAcceptedJobs, [partnerId]),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, _i16.Job>> listenToJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToJob, [jobId]),
            returnValue: _i7.Stream<_i3.Either<_i8.Failure, _i16.Job>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, _i16.Job>>);

  @override
  _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>> listenToActiveJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToActiveJobs, [partnerId]),
            returnValue:
                _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>.empty(),
          )
          as _i7.Stream<_i3.Either<_i8.Failure, List<_i16.Job>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>> getPartnerEarnings(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerEarnings, [partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>>.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerEarnings>(
                    this,
                    Invocation.method(#getPartnerEarnings, [partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>>
  updatePartnerEarnings(String? partnerId, double? jobEarnings) =>
      (super.noSuchMethod(
            Invocation.method(#updatePartnerEarnings, [partnerId, jobEarnings]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>>.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerEarnings>(
                    this,
                    Invocation.method(#updatePartnerEarnings, [
                      partnerId,
                      jobEarnings,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerEarnings>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, List<_i17.DailyEarning>>>
  getEarningsByDateRange(
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getEarningsByDateRange, [
              partnerId,
              startDate,
              endDate,
            ]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, List<_i17.DailyEarning>>
                >.value(
                  _FakeEither_1<_i8.Failure, List<_i17.DailyEarning>>(
                    this,
                    Invocation.method(#getEarningsByDateRange, [
                      partnerId,
                      startDate,
                      endDate,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, List<_i17.DailyEarning>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  getPartnerAvailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAvailability, [partnerId]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#getPartnerAvailability, [partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  updateAvailabilityStatus(
    String? partnerId,
    bool? isAvailable,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailabilityStatus, [
              partnerId,
              isAvailable,
              reason,
            ]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#updateAvailabilityStatus, [
                      partnerId,
                      isAvailable,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  updateOnlineStatus(String? partnerId, bool? isOnline) =>
      (super.noSuchMethod(
            Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#updateOnlineStatus, [
                      partnerId,
                      isOnline,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  updateWorkingHours(
    String? partnerId,
    Map<String, List<String>>? workingHours,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorkingHours, [partnerId, workingHours]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#updateWorkingHours, [
                      partnerId,
                      workingHours,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>> blockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#blockDates, [partnerId, dates]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#blockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>> unblockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unblockDates, [partnerId, dates]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#unblockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  setTemporaryUnavailability(
    String? partnerId,
    DateTime? unavailableUntil,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTemporaryUnavailability, [
              partnerId,
              unavailableUntil,
              reason,
            ]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#setTemporaryUnavailability, [
                      partnerId,
                      unavailableUntil,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>
  clearTemporaryUnavailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#clearTemporaryUnavailability, [partnerId]),
            returnValue:
                _i7.Future<
                  _i3.Either<_i8.Failure, _i17.PartnerAvailability>
                >.value(
                  _FakeEither_1<_i8.Failure, _i17.PartnerAvailability>(
                    this,
                    Invocation.method(#clearTemporaryUnavailability, [
                      partnerId,
                    ]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, _i17.PartnerAvailability>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>> getJobStatistics(
    String? partnerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobStatistics,
              [partnerId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_1<_i8.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #getJobStatistics,
                      [partnerId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>>
  getPerformanceMetrics(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, [partnerId]),
            returnValue:
                _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_1<_i8.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#getPerformanceMetrics, [partnerId]),
                  ),
                ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, Map<String, dynamic>>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> markJobNotificationAsRead(
    String? partnerId,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markJobNotificationAsRead, [partnerId, jobId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#markJobNotificationAsRead, [
                  partnerId,
                  jobId,
                ]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, int>> getUnreadNotificationsCount(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotificationsCount, [partnerId]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, int>>.value(
              _FakeEither_1<_i8.Failure, int>(
                this,
                Invocation.method(#getUnreadNotificationsCount, [partnerId]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, int>>);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i18.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i7.Future<void> initialize({
    _i19.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i18.LogEntry> getRecentLogs({
    int? limit = 100,
    _i18.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i18.LogEntry>[],
          )
          as List<_i18.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i7.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i19.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_2(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i4.FirebaseAnalytics);

  @override
  _i5.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_3(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i5.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i7.Future<_i5.Trace?>.value(),
          )
          as _i7.Future<_i5.Trace?>);

  @override
  _i7.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i5.HttpMetric?> startHttpMetric(
    String? url,
    _i5.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i7.Future<_i5.HttpMetric?>.value(),
          )
          as _i7.Future<_i5.HttpMetric?>);

  @override
  _i7.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i20.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i21.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i22.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i23.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i7.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<_i3.Either<_i8.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i7.Future<_i3.Either<_i8.Failure, void>>.value(
              _FakeEither_1<_i8.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i7.Future<_i3.Either<_i8.Failure, void>>);

  @override
  _i7.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i7.Future<String?>.value(),
          )
          as _i7.Future<String?>);

  @override
  _i7.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i24.NotificationPriority? priority = _i24.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i24.NotificationPriority? priority = _i24.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i24.NotificationPriority? priority = _i24.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i24.NotificationPriority? priority = _i24.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);
}

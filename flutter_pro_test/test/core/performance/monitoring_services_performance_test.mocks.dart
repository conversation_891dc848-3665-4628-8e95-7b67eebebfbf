// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/core/performance/monitoring_services_performance_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:dartz/dartz.dart' as _i4;
import 'package:firebase_analytics/firebase_analytics.dart' as _i2;
import 'package:firebase_performance/firebase_performance.dart' as _i3;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i7;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i12;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i13;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i10;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i9;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i11;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:shared_preferences/src/shared_preferences_legacy.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAnalytics_0 extends _i1.SmartFake
    implements _i2.FirebaseAnalytics {
  _FakeFirebaseAnalytics_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_1 extends _i1.SmartFake
    implements _i3.FirebasePerformance {
  _FakeFirebasePerformance_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_2<L, R> extends _i1.SmartFake implements _i4.Either<L, R> {
  _FakeEither_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SharedPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedPreferences extends _i1.Mock implements _i5.SharedPreferences {
  MockSharedPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> getKeys() =>
      (super.noSuchMethod(
            Invocation.method(#getKeys, []),
            returnValue: <String>{},
          )
          as Set<String>);

  @override
  Object? get(String? key) =>
      (super.noSuchMethod(Invocation.method(#get, [key])) as Object?);

  @override
  bool? getBool(String? key) =>
      (super.noSuchMethod(Invocation.method(#getBool, [key])) as bool?);

  @override
  int? getInt(String? key) =>
      (super.noSuchMethod(Invocation.method(#getInt, [key])) as int?);

  @override
  double? getDouble(String? key) =>
      (super.noSuchMethod(Invocation.method(#getDouble, [key])) as double?);

  @override
  String? getString(String? key) =>
      (super.noSuchMethod(Invocation.method(#getString, [key])) as String?);

  @override
  bool containsKey(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#containsKey, [key]),
            returnValue: false,
          )
          as bool);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(#getStringList, [key]))
          as List<String>?);

  @override
  _i6.Future<bool> setBool(String? key, bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#setBool, [key, value]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> setInt(String? key, int? value) =>
      (super.noSuchMethod(
            Invocation.method(#setInt, [key, value]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> setDouble(String? key, double? value) =>
      (super.noSuchMethod(
            Invocation.method(#setDouble, [key, value]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> setString(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setString, [key, value]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> setStringList(String? key, List<String>? value) =>
      (super.noSuchMethod(
            Invocation.method(#setStringList, [key, value]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> remove(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#remove, [key]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> commit() =>
      (super.noSuchMethod(
            Invocation.method(#commit, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<bool> clear() =>
      (super.noSuchMethod(
            Invocation.method(#clear, []),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  _i6.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i7.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_0(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i2.FirebaseAnalytics);

  @override
  _i3.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_1(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i3.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i6.Future<_i3.Trace?>.value(),
          )
          as _i6.Future<_i3.Trace?>);

  @override
  _i6.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.HttpMetric?> startHttpMetric(
    String? url,
    _i3.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i6.Future<_i3.HttpMetric?>.value(),
          )
          as _i6.Future<_i3.HttpMetric?>);

  @override
  _i6.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i8.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i9.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i10.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i11.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.Either<_i12.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i6.Future<_i4.Either<_i12.Failure, void>>.value(
              _FakeEither_2<_i12.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i6.Future<_i4.Either<_i12.Failure, void>>);

  @override
  _i6.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i13.NotificationPriority? priority = _i13.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i13.NotificationPriority? priority = _i13.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i13.NotificationPriority? priority = _i13.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i13.NotificationPriority? priority = _i13.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/integration/client_booking_error_handling_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:dartz/dartz.dart' as _i3;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i7;
import 'package:flutter_pro_test/core/usecases/usecase.dart' as _i9;
import 'package:flutter_pro_test/features/booking/domain/entities/booking.dart'
    as _i13;
import 'package:flutter_pro_test/features/booking/domain/entities/partner.dart'
    as _i11;
import 'package:flutter_pro_test/features/booking/domain/entities/service.dart'
    as _i8;
import 'package:flutter_pro_test/features/client/domain/entities/booking_request.dart'
    as _i14;
import 'package:flutter_pro_test/features/client/domain/entities/payment_request.dart'
    as _i17;
import 'package:flutter_pro_test/features/client/domain/entities/payment_result.dart'
    as _i16;
import 'package:flutter_pro_test/features/client/domain/repositories/client_service_repository.dart'
    as _i2;
import 'package:flutter_pro_test/features/client/domain/repositories/payment_repository.dart'
    as _i4;
import 'package:flutter_pro_test/features/client/domain/usecases/create_booking.dart'
    as _i12;
import 'package:flutter_pro_test/features/client/domain/usecases/get_available_services.dart'
    as _i5;
import 'package:flutter_pro_test/features/client/domain/usecases/get_client_bookings.dart'
    as _i18;
import 'package:flutter_pro_test/features/client/domain/usecases/process_payment.dart'
    as _i15;
import 'package:flutter_pro_test/features/client/domain/usecases/search_available_partners.dart'
    as _i10;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeClientServiceRepository_0 extends _i1.SmartFake
    implements _i2.ClientServiceRepository {
  _FakeClientServiceRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaymentRepository_2 extends _i1.SmartFake
    implements _i4.PaymentRepository {
  _FakePaymentRepository_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetAvailableServices].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAvailableServices extends _i1.Mock
    implements _i5.GetAvailableServices {
  MockGetAvailableServices() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ClientServiceRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeClientServiceRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.ClientServiceRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, List<_i8.Service>>> call(
    _i9.NoParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i3.Either<_i7.Failure, List<_i8.Service>>>.value(
                  _FakeEither_1<_i7.Failure, List<_i8.Service>>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, List<_i8.Service>>>);
}

/// A class which mocks [SearchAvailablePartners].
///
/// See the documentation for Mockito's code generation for more information.
class MockSearchAvailablePartners extends _i1.Mock
    implements _i10.SearchAvailablePartners {
  MockSearchAvailablePartners() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ClientServiceRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeClientServiceRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.ClientServiceRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, List<_i11.Partner>>> call(
    _i10.SearchAvailablePartnersParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i3.Either<_i7.Failure, List<_i11.Partner>>>.value(
                  _FakeEither_1<_i7.Failure, List<_i11.Partner>>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, List<_i11.Partner>>>);
}

/// A class which mocks [CreateBooking].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreateBooking extends _i1.Mock implements _i12.CreateBooking {
  MockCreateBooking() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ClientServiceRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeClientServiceRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.ClientServiceRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, _i13.Booking>> call(
    _i14.BookingRequest? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i3.Either<_i7.Failure, _i13.Booking>>.value(
                  _FakeEither_1<_i7.Failure, _i13.Booking>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, _i13.Booking>>);
}

/// A class which mocks [ProcessPayment].
///
/// See the documentation for Mockito's code generation for more information.
class MockProcessPayment extends _i1.Mock implements _i15.ProcessPayment {
  MockProcessPayment() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.PaymentRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakePaymentRepository_2(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i4.PaymentRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, _i16.PaymentResult>> call(
    _i17.PaymentRequest? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i3.Either<_i7.Failure, _i16.PaymentResult>>.value(
                  _FakeEither_1<_i7.Failure, _i16.PaymentResult>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, _i16.PaymentResult>>);
}

/// A class which mocks [GetAvailablePaymentMethods].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetAvailablePaymentMethods extends _i1.Mock
    implements _i15.GetAvailablePaymentMethods {
  MockGetAvailablePaymentMethods() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.PaymentRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakePaymentRepository_2(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i4.PaymentRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, List<_i17.PaymentMethod>>> call(
    _i9.NoParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<
                  _i3.Either<_i7.Failure, List<_i17.PaymentMethod>>
                >.value(
                  _FakeEither_1<_i7.Failure, List<_i17.PaymentMethod>>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, List<_i17.PaymentMethod>>>);
}

/// A class which mocks [GetClientBookings].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetClientBookings extends _i1.Mock implements _i18.GetClientBookings {
  MockGetClientBookings() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ClientServiceRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeClientServiceRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.ClientServiceRepository);

  @override
  _i6.Future<_i3.Either<_i7.Failure, List<_i13.Booking>>> call(
    _i18.GetClientBookingsParams? params,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#call, [params]),
            returnValue:
                _i6.Future<_i3.Either<_i7.Failure, List<_i13.Booking>>>.value(
                  _FakeEither_1<_i7.Failure, List<_i13.Booking>>(
                    this,
                    Invocation.method(#call, [params]),
                  ),
                ),
          )
          as _i6.Future<_i3.Either<_i7.Failure, List<_i13.Booking>>>);
}

name: 📦 CareNow MVP - Release Management

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., 1.0.0)'
        required: true
        type: string
      release_type:
        description: 'Type of release'
        required: true
        default: 'minor'
        type: choice
        options:
        - patch
        - minor
        - major
        - prerelease
      create_tag:
        description: 'Create git tag'
        required: false
        default: true
        type: boolean
      draft:
        description: 'Create as draft release'
        required: false
        default: false
        type: boolean

env:
  FLUTTER_VERSION: '3.8.1'

jobs:
  # Job 1: Release Preparation
  prepare:
    name: 🔧 Release Preparation
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      tag: ${{ steps.version.outputs.tag }}
      changelog: ${{ steps.changelog.outputs.changelog }}
      is-prerelease: ${{ steps.version.outputs.is-prerelease }}
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 📊 Determine Version
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          VERSION="${{ github.event.inputs.version }}"
          TAG="v$VERSION"
          IS_PRERELEASE="false"
          
          if [[ "$VERSION" == *"alpha"* ]] || [[ "$VERSION" == *"beta"* ]] || [[ "$VERSION" == *"rc"* ]]; then
            IS_PRERELEASE="true"
          fi
        else
          TAG="${{ github.ref_name }}"
          VERSION="${TAG#v}"
          IS_PRERELEASE="false"
          
          if [[ "$VERSION" == *"alpha"* ]] || [[ "$VERSION" == *"beta"* ]] || [[ "$VERSION" == *"rc"* ]]; then
            IS_PRERELEASE="true"
          fi
        fi
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "tag=$TAG" >> $GITHUB_OUTPUT
        echo "is-prerelease=$IS_PRERELEASE" >> $GITHUB_OUTPUT
        
        echo "Release version: $VERSION"
        echo "Release tag: $TAG"
        echo "Is prerelease: $IS_PRERELEASE"
    
    - name: 📝 Generate Changelog
      id: changelog
      run: |
        echo "📝 Generating changelog..."
        
        # Get the latest tag (excluding current)
        PREVIOUS_TAG=$(git tag --sort=-version:refname | grep -v "${{ steps.version.outputs.tag }}" | head -n 1)
        
        if [ -z "$PREVIOUS_TAG" ]; then
          echo "No previous tag found, using initial commit"
          PREVIOUS_TAG=$(git rev-list --max-parents=0 HEAD)
        fi
        
        echo "Generating changelog from $PREVIOUS_TAG to ${{ steps.version.outputs.tag }}"
        
        # Generate changelog
        CHANGELOG=$(cat << 'EOF'
        ## 🚀 What's New in ${{ steps.version.outputs.version }}
        
        ### ✨ Features
        $(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s" --grep="feat:" --grep="feature:" | head -10)
        
        ### 🐛 Bug Fixes
        $(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s" --grep="fix:" --grep="bug:" | head -10)
        
        ### 🔧 Improvements
        $(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s" --grep="improve:" --grep="enhance:" | head -10)
        
        ### 📚 Documentation
        $(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s" --grep="docs:" | head -5)
        
        ### 🔒 Security
        $(git log $PREVIOUS_TAG..HEAD --pretty=format:"- %s" --grep="security:" --grep="sec:" | head -5)
        
        ---
        
        **Full Changelog**: https://github.com/${{ github.repository }}/compare/$PREVIOUS_TAG...${{ steps.version.outputs.tag }}
        EOF
        )
        
        # Save changelog to file and output
        echo "$CHANGELOG" > CHANGELOG.md
        echo "changelog<<EOF" >> $GITHUB_OUTPUT
        echo "$CHANGELOG" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT
    
    - name: 🏷️ Create Git Tag
      if: github.event_name == 'workflow_dispatch' && github.event.inputs.create_tag == 'true'
      run: |
        echo "🏷️ Creating git tag: ${{ steps.version.outputs.tag }}"
        
        git config user.name "github-actions[bot]"
        git config user.email "github-actions[bot]@users.noreply.github.com"
        
        git tag -a "${{ steps.version.outputs.tag }}" -m "Release ${{ steps.version.outputs.version }}"
        git push origin "${{ steps.version.outputs.tag }}"
        
        echo "✅ Git tag created and pushed"

  # Job 2: Build Release Artifacts
  build:
    name: 🏗️ Build Release Artifacts
    needs: prepare
    uses: ./.github/workflows/build.yml
    with:
      platforms: 'all'
      environment: 'production'
    secrets: inherit

  # Job 3: Create GitHub Release
  release:
    name: 📦 Create GitHub Release
    runs-on: ubuntu-latest
    needs: [prepare, build]
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
    
    - name: 📦 Download All Build Artifacts
      uses: actions/download-artifact@v4
      with:
        path: release-artifacts/
    
    - name: 📋 Prepare Release Assets
      run: |
        echo "📋 Preparing release assets..."
        
        mkdir -p release-assets
        
        # Web build
        if [ -d "release-artifacts/web-build-production" ]; then
          cd release-artifacts/web-build-production
          zip -r ../../release-assets/carenow-web-${{ needs.prepare.outputs.version }}.zip .
          cd ../..
        fi
        
        # Android builds
        if [ -d "release-artifacts/android-build-production" ]; then
          find release-artifacts/android-build-production -name "*.apk" -exec cp {} release-assets/ \;
          find release-artifacts/android-build-production -name "*.aab" -exec cp {} release-assets/ \;
        fi
        
        # iOS build
        if [ -d "release-artifacts/ios-build-production" ]; then
          cd release-artifacts/ios-build-production
          zip -r ../../release-assets/carenow-ios-${{ needs.prepare.outputs.version }}.zip .
          cd ../..
        fi
        
        # List all assets
        echo "Release assets:"
        ls -la release-assets/
    
    - name: 📊 Generate Release Notes
      run: |
        cat > RELEASE_NOTES.md << 'EOF'
        # 🎉 CareNow MVP v${{ needs.prepare.outputs.version }}
        
        ${{ needs.prepare.outputs.changelog }}
        
        ## 📦 Release Assets
        
        ### 🌐 Web Application
        - **carenow-web-${{ needs.prepare.outputs.version }}.zip** - Web application build
        - Deploy to any web server or hosting platform
        
        ### 🤖 Android Application
        - **app-release.apk** - Android APK for direct installation
        - **app-release.aab** - Android App Bundle for Google Play Store
        
        ### 🍎 iOS Application
        - **carenow-ios-${{ needs.prepare.outputs.version }}.zip** - iOS application build
        - Requires code signing and App Store submission
        
        ## 🚀 Deployment Instructions
        
        ### Web Deployment
        1. Extract `carenow-web-${{ needs.prepare.outputs.version }}.zip`
        2. Upload contents to your web server
        3. Configure environment variables
        4. Access your deployed application
        
        ### Android Deployment
        1. **APK**: Install directly on Android devices
        2. **AAB**: Upload to Google Play Console
        
        ### iOS Deployment
        1. Extract iOS build
        2. Open in Xcode
        3. Archive and upload to App Store Connect
        
        ## 🔧 System Requirements
        
        - **Web**: Modern web browser with JavaScript enabled
        - **Android**: Android 5.0 (API level 21) or higher
        - **iOS**: iOS 12.0 or higher
        
        ## 🆘 Support
        
        - 📖 Documentation: [Project README](https://github.com/${{ github.repository }})
        - 🐛 Issues: [GitHub Issues](https://github.com/${{ github.repository }}/issues)
        - 💬 Discussions: [GitHub Discussions](https://github.com/${{ github.repository }}/discussions)
        
        ---
        
        **Built with ❤️ using Flutter ${{ env.FLUTTER_VERSION }}**
        EOF
    
    - name: 🚀 Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ needs.prepare.outputs.tag }}
        name: 'CareNow MVP v${{ needs.prepare.outputs.version }}'
        body_path: RELEASE_NOTES.md
        draft: ${{ github.event.inputs.draft == 'true' }}
        prerelease: ${{ needs.prepare.outputs.is-prerelease }}
        files: |
          release-assets/*
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 📊 Release Summary
      run: |
        echo "## 📦 Release Created Successfully!" >> $GITHUB_STEP_SUMMARY
        echo "- **Version**: ${{ needs.prepare.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Tag**: ${{ needs.prepare.outputs.tag }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Prerelease**: ${{ needs.prepare.outputs.is-prerelease }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Assets**: $(ls release-assets/ | wc -l) files" >> $GITHUB_STEP_SUMMARY
        echo "- **Release URL**: https://github.com/${{ github.repository }}/releases/tag/${{ needs.prepare.outputs.tag }}" >> $GITHUB_STEP_SUMMARY

  # Job 4: Post-Release Tasks
  post-release:
    name: 🎯 Post-Release Tasks
    runs-on: ubuntu-latest
    needs: [prepare, release]
    if: needs.prepare.outputs.is-prerelease == 'false'
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 📊 Update Version in pubspec.yaml
      run: |
        echo "📊 Updating version in pubspec.yaml..."
        
        # Update version in pubspec.yaml for next development cycle
        CURRENT_VERSION="${{ needs.prepare.outputs.version }}"
        
        # Increment patch version for next development
        IFS='.' read -ra VERSION_PARTS <<< "$CURRENT_VERSION"
        MAJOR=${VERSION_PARTS[0]}
        MINOR=${VERSION_PARTS[1]}
        PATCH=${VERSION_PARTS[2]}
        
        NEXT_PATCH=$((PATCH + 1))
        NEXT_VERSION="$MAJOR.$MINOR.$NEXT_PATCH"
        
        # Update pubspec.yaml
        sed -i "s/^version: .*/version: $NEXT_VERSION+1/" flutter_pro_test/pubspec.yaml
        
        echo "Updated version to $NEXT_VERSION+1 for next development cycle"
    
    - name: 📝 Create Development Branch
      run: |
        echo "📝 Creating development branch for next version..."
        
        git config user.name "github-actions[bot]"
        git config user.email "github-actions[bot]@users.noreply.github.com"
        
        git add flutter_pro_test/pubspec.yaml
        git commit -m "chore: bump version for next development cycle"
        git push origin main
        
        echo "✅ Version updated for next development cycle"
    
    - name: 🎉 Post-Release Summary
      run: |
        echo "## 🎉 Post-Release Tasks Completed!" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Version bumped for next development cycle" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Changes committed to main branch" >> $GITHUB_STEP_SUMMARY
        echo "- 🚀 Ready for next development iteration" >> $GITHUB_STEP_SUMMARY

// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/core/monitoring/production_monitoring_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:firebase_analytics/firebase_analytics.dart' as _i2;
import 'package:firebase_performance/firebase_performance.dart' as _i3;
import 'package:flutter_pro_test/core/analytics/firebase_analytics_service.dart'
    as _i7;
import 'package:flutter_pro_test/core/error_tracking/error_tracking_service.dart'
    as _i8;
import 'package:flutter_pro_test/core/monitoring/monitoring_service.dart'
    as _i5;
import 'package:flutter_pro_test/core/performance/performance_manager.dart'
    as _i10;
import 'package:flutter_pro_test/core/security/advanced_security_manager.dart'
    as _i4;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i11;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseAnalytics_0 extends _i1.SmartFake
    implements _i2.FirebaseAnalytics {
  _FakeFirebaseAnalytics_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebasePerformance_1 extends _i1.SmartFake
    implements _i3.FirebasePerformance {
  _FakeFirebasePerformance_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSecurityHealthReport_2 extends _i1.SmartFake
    implements _i4.SecurityHealthReport {
  _FakeSecurityHealthReport_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [MonitoringService].
///
/// See the documentation for Mockito's code generation for more information.
class MockMonitoringService extends _i1.Mock implements _i5.MonitoringService {
  MockMonitoringService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAnalyticsEnabled =>
      (super.noSuchMethod(
            Invocation.getter(#isAnalyticsEnabled),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Future<void> initialize({
    _i7.FirebaseAnalyticsService? analyticsService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void logDebug(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logDebug, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logInfo(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logInfo, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logWarning(String? message, {Map<String, dynamic>? metadata}) =>
      super.noSuchMethod(
        Invocation.method(#logWarning, [message], {#metadata: metadata}),
        returnValueForMissingStub: null,
      );

  @override
  void logError(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logError,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void logCritical(
    String? message, {
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #logCritical,
      [message],
      {#error: error, #stackTrace: stackTrace, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  List<_i5.LogEntry> getRecentLogs({
    int? limit = 100,
    _i5.LogLevel? minLevel,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentLogs, [], {
              #limit: limit,
              #minLevel: minLevel,
            }),
            returnValue: <_i5.LogEntry>[],
          )
          as List<_i5.LogEntry>);

  @override
  Map<String, dynamic> getErrorStats() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  Map<String, dynamic> getHealthStatus() =>
      (super.noSuchMethod(
            Invocation.method(#getHealthStatus, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  void clearLogs() => super.noSuchMethod(
    Invocation.method(#clearLogs, []),
    returnValueForMissingStub: null,
  );

  @override
  _i6.Future<void> trackPerformanceMetric({
    required String? metricName,
    required Duration? duration,
    Map<String, Object?>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceMetric, [], {
              #metricName: metricName,
              #duration: duration,
              #additionalData: additionalData,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackError({
    required String? errorType,
    required dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #error: error,
              #stackTrace: stackTrace,
              #metadata: metadata,
              #fatal: fatal,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackUserAction({
    required String? actionName,
    String? screenName,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackUserAction, [], {
              #actionName: actionName,
              #screenName: screenName,
              #parameters: parameters,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [FirebaseAnalyticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAnalyticsService extends _i1.Mock
    implements _i7.FirebaseAnalyticsService {
  MockFirebaseAnalyticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseAnalytics get analytics =>
      (super.noSuchMethod(
            Invocation.getter(#analytics),
            returnValue: _FakeFirebaseAnalytics_0(
              this,
              Invocation.getter(#analytics),
            ),
          )
          as _i2.FirebaseAnalytics);

  @override
  _i3.FirebasePerformance get performance =>
      (super.noSuchMethod(
            Invocation.getter(#performance),
            returnValue: _FakeFirebasePerformance_1(
              this,
              Invocation.getter(#performance),
            ),
          )
          as _i3.FirebasePerformance);

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setUserId(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#setUserId, [userId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setUserType(String? userType) =>
      (super.noSuchMethod(
            Invocation.method(#setUserType, [userType]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logEvent(
    String? eventName, {
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #logEvent,
              [eventName],
              {#parameters: parameters},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logScreenView({
    required String? screenName,
    String? screenClass,
    Map<String, Object?>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#logScreenView, [], {
              #screenName: screenName,
              #screenClass: screenClass,
              #parameters: parameters,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? metadata,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #recordError,
              [error, stackTrace],
              {#metadata: metadata, #fatal: fatal},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.Trace?> startTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#startTrace, [traceName]),
            returnValue: _i6.Future<_i3.Trace?>.value(),
          )
          as _i6.Future<_i3.Trace?>);

  @override
  _i6.Future<void> stopTrace(String? traceName) =>
      (super.noSuchMethod(
            Invocation.method(#stopTrace, [traceName]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setTraceAttribute(
    String? traceName,
    String? attribute,
    String? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTraceAttribute, [
              traceName,
              attribute,
              value,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i3.HttpMetric?> startHttpMetric(
    String? url,
    _i3.HttpMethod? httpMethod,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startHttpMetric, [url, httpMethod]),
            returnValue: _i6.Future<_i3.HttpMetric?>.value(),
          )
          as _i6.Future<_i3.HttpMetric?>);

  @override
  _i6.Future<void> stopHttpMetric(
    String? url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #stopHttpMetric,
              [url],
              {
                #responseCode: responseCode,
                #requestPayloadSize: requestPayloadSize,
                #responsePayloadSize: responsePayloadSize,
              },
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [ErrorTrackingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockErrorTrackingService extends _i1.Mock
    implements _i8.ErrorTrackingService {
  MockErrorTrackingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isInitialized =>
      (super.noSuchMethod(Invocation.getter(#isInitialized), returnValue: false)
          as bool);

  @override
  _i6.Future<void> initialize({
    required _i7.FirebaseAnalyticsService? analyticsService,
    required _i5.MonitoringService? monitoringService,
    required _i9.NotificationService? notificationService,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#initialize, [], {
              #analyticsService: analyticsService,
              #monitoringService: monitoringService,
              #notificationService: notificationService,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackError({
    required String? errorType,
    required String? errorMessage,
    required dynamic error,
    StackTrace? stackTrace,
    String? userId,
    String? screenName,
    String? userAction,
    Map<String, dynamic>? metadata,
    _i8.ErrorSeverity? severity = _i8.ErrorSeverity.medium,
    bool? fatal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackError, [], {
              #errorType: errorType,
              #errorMessage: errorMessage,
              #error: error,
              #stackTrace: stackTrace,
              #userId: userId,
              #screenName: screenName,
              #userAction: userAction,
              #metadata: metadata,
              #severity: severity,
              #fatal: fatal,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> trackPerformanceDegradation({
    required String? metricName,
    required double? currentValue,
    required double? threshold,
    String? context,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#trackPerformanceDegradation, [], {
              #metricName: metricName,
              #currentValue: currentValue,
              #threshold: threshold,
              #context: context,
              #metadata: metadata,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void setErrorThreshold({
    required String? errorType,
    required int? maxOccurrences,
    required Duration? timeWindow,
    _i8.ErrorSeverity? alertSeverity = _i8.ErrorSeverity.high,
  }) => super.noSuchMethod(
    Invocation.method(#setErrorThreshold, [], {
      #errorType: errorType,
      #maxOccurrences: maxOccurrences,
      #timeWindow: timeWindow,
      #alertSeverity: alertSeverity,
    }),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getErrorStatistics() =>
      (super.noSuchMethod(
            Invocation.method(#getErrorStatistics, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  List<_i8.ErrorIncident> getRecentErrors({int? limit = 20}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentErrors, [], {#limit: limit}),
            returnValue: <_i8.ErrorIncident>[],
          )
          as List<_i8.ErrorIncident>);

  @override
  List<_i8.ErrorIncident> getErrorsByType(String? errorType) =>
      (super.noSuchMethod(
            Invocation.method(#getErrorsByType, [errorType]),
            returnValue: <_i8.ErrorIncident>[],
          )
          as List<_i8.ErrorIncident>);

  @override
  _i6.Future<void> clearErrorHistory() =>
      (super.noSuchMethod(
            Invocation.method(#clearErrorHistory, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PerformanceManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockPerformanceManager extends _i1.Mock
    implements _i10.PerformanceManager {
  MockPerformanceManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void cacheData(String? key, dynamic data, {Duration? expiration}) =>
      super.noSuchMethod(
        Invocation.method(#cacheData, [key, data], {#expiration: expiration}),
        returnValueForMissingStub: null,
      );

  @override
  T? getCachedData<T>(String? key) =>
      (super.noSuchMethod(Invocation.method(#getCachedData, [key])) as T?);

  @override
  bool isCached(String? key) =>
      (super.noSuchMethod(
            Invocation.method(#isCached, [key]),
            returnValue: false,
          )
          as bool);

  @override
  void clearCache(String? key) => super.noSuchMethod(
    Invocation.method(#clearCache, [key]),
    returnValueForMissingStub: null,
  );

  @override
  void clearAllCache() => super.noSuchMethod(
    Invocation.method(#clearAllCache, []),
    returnValueForMissingStub: null,
  );

  @override
  void recordEvent(
    String? name, {
    Duration? duration,
    Map<String, dynamic>? metadata,
  }) => super.noSuchMethod(
    Invocation.method(
      #recordEvent,
      [name],
      {#duration: duration, #metadata: metadata},
    ),
    returnValueForMissingStub: null,
  );

  @override
  Map<String, dynamic> getPerformanceStats() =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceStats, []),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  List<_i10.PerformanceEvent> getRecentEvents({int? limit = 50}) =>
      (super.noSuchMethod(
            Invocation.method(#getRecentEvents, [], {#limit: limit}),
            returnValue: <_i10.PerformanceEvent>[],
          )
          as List<_i10.PerformanceEvent>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AdvancedSecurityManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdvancedSecurityManager extends _i1.Mock
    implements _i4.AdvancedSecurityManager {
  MockAdvancedSecurityManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  bool verifyCertificatePin(String? host, String? certificateHash) =>
      (super.noSuchMethod(
            Invocation.method(#verifyCertificatePin, [host, certificateHash]),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Future<String> encryptDataAdvanced(String? data, {String? customKey}) =>
      (super.noSuchMethod(
            Invocation.method(
              #encryptDataAdvanced,
              [data],
              {#customKey: customKey},
            ),
            returnValue: _i6.Future<String>.value(
              _i11.dummyValue<String>(
                this,
                Invocation.method(
                  #encryptDataAdvanced,
                  [data],
                  {#customKey: customKey},
                ),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<String> decryptDataAdvanced(
    String? encryptedData, {
    String? customKey,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #decryptDataAdvanced,
              [encryptedData],
              {#customKey: customKey},
            ),
            returnValue: _i6.Future<String>.value(
              _i11.dummyValue<String>(
                this,
                Invocation.method(
                  #decryptDataAdvanced,
                  [encryptedData],
                  {#customKey: customKey},
                ),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  List<Map<String, dynamic>> getSecurityViolations() =>
      (super.noSuchMethod(
            Invocation.method(#getSecurityViolations, []),
            returnValue: <Map<String, dynamic>>[],
          )
          as List<Map<String, dynamic>>);

  @override
  _i6.Future<_i4.SecurityHealthReport> performSecurityHealthCheck() =>
      (super.noSuchMethod(
            Invocation.method(#performSecurityHealthCheck, []),
            returnValue: _i6.Future<_i4.SecurityHealthReport>.value(
              _FakeSecurityHealthReport_2(
                this,
                Invocation.method(#performSecurityHealthCheck, []),
              ),
            ),
          )
          as _i6.Future<_i4.SecurityHealthReport>);
}

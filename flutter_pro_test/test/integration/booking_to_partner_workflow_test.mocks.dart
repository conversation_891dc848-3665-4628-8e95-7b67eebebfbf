// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/integration/booking_to_partner_workflow_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i5;
import 'package:flutter_pro_test/features/booking/domain/entities/booking.dart'
    as _i6;
import 'package:flutter_pro_test/features/booking/domain/entities/booking_request.dart'
    as _i7;
import 'package:flutter_pro_test/features/booking/domain/repositories/booking_repository.dart'
    as _i3;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i15;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i13;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i12;
import 'package:flutter_pro_test/features/partner/domain/entities/job.dart'
    as _i9;
import 'package:flutter_pro_test/features/partner/domain/entities/partner_earnings.dart'
    as _i10;
import 'package:flutter_pro_test/features/partner/domain/repositories/partner_job_repository.dart'
    as _i8;
import 'package:flutter_pro_test/shared/services/notification_action_handler.dart'
    as _i14;
import 'package:flutter_pro_test/shared/services/notification_service.dart'
    as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [BookingRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookingRepository extends _i1.Mock implements _i3.BookingRepository {
  MockBookingRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> createBooking(
    _i7.BookingRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createBooking, [request]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#createBooking, [request]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> getBookingById(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingById, [bookingId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#getBookingById, [bookingId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>> getUserBookings(
    String? userId, {
    _i6.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Booking>>(
                    this,
                    Invocation.method(
                      #getUserBookings,
                      [userId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>> getPartnerBookings(
    String? partnerId, {
    _i6.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Booking>>(
                    this,
                    Invocation.method(
                      #getPartnerBookings,
                      [partnerId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>> getBookingsByDateRange(
    String? userId,
    DateTime? startDate,
    DateTime? endDate, {
    bool? isPartner = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getBookingsByDateRange,
              [userId, startDate, endDate],
              {#isPartner: isPartner},
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Booking>>(
                    this,
                    Invocation.method(
                      #getBookingsByDateRange,
                      [userId, startDate, endDate],
                      {#isPartner: isPartner},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Booking>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> updateBookingStatus(
    String? bookingId,
    _i6.BookingStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateBookingStatus, [bookingId, status]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#updateBookingStatus, [bookingId, status]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> cancelBooking(
    String? bookingId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelBooking, [bookingId, cancellationReason]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#cancelBooking, [
                  bookingId,
                  cancellationReason,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> confirmBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#confirmBooking, [bookingId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#confirmBooking, [bookingId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> startBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startBooking, [bookingId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#startBooking, [bookingId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>> completeBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeBooking, [bookingId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>.value(
              _FakeEither_0<_i5.Failure, _i6.Booking>(
                this,
                Invocation.method(#completeBooking, [bookingId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Booking>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>> listenToUserBookings(
    String? userId, {
    _i6.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>>
  listenToPartnerBookings(
    String? partnerId, {
    _i6.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Booking>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i6.Booking>> listenToBooking(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToBooking, [bookingId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, _i6.Booking>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i6.Booking>>);
}

/// A class which mocks [PartnerJobRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerJobRepository extends _i1.Mock
    implements _i8.PartnerJobRepository {
  MockPartnerJobRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>> getPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPendingJobs, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i9.Job>>(
                    this,
                    Invocation.method(#getPendingJobs, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>> getAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAcceptedJobs, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i9.Job>>(
                    this,
                    Invocation.method(#getAcceptedJobs, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>> getJobHistory(
    String? partnerId, {
    _i9.JobStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobHistory,
              [partnerId],
              {
                #status: status,
                #startDate: startDate,
                #endDate: endDate,
                #limit: limit,
              },
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i9.Job>>(
                    this,
                    Invocation.method(
                      #getJobHistory,
                      [partnerId],
                      {
                        #status: status,
                        #startDate: startDate,
                        #endDate: endDate,
                        #limit: limit,
                      },
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> getJobById(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [jobId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#getJobById, [jobId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> acceptJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#acceptJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#acceptJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> rejectJob(
    String? jobId,
    String? partnerId,
    String? rejectionReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rejectJob, [jobId, partnerId, rejectionReason]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#rejectJob, [
                  jobId,
                  partnerId,
                  rejectionReason,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> startJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#startJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> completeJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#completeJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.Job>> cancelJob(
    String? jobId,
    String? partnerId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelJob, [
              jobId,
              partnerId,
              cancellationReason,
            ]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>.value(
              _FakeEither_0<_i5.Failure, _i9.Job>(
                this,
                Invocation.method(#cancelJob, [
                  jobId,
                  partnerId,
                  cancellationReason,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>> listenToPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToPendingJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>> listenToAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToAcceptedJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i9.Job>> listenToJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToJob, [jobId]),
            returnValue: _i4.Stream<_i2.Either<_i5.Failure, _i9.Job>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i9.Job>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>> listenToActiveJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToActiveJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i9.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>> getPartnerEarnings(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerEarnings, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>>.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerEarnings>(
                    this,
                    Invocation.method(#getPartnerEarnings, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>>
  updatePartnerEarnings(String? partnerId, double? jobEarnings) =>
      (super.noSuchMethod(
            Invocation.method(#updatePartnerEarnings, [partnerId, jobEarnings]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>>.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerEarnings>(
                    this,
                    Invocation.method(#updatePartnerEarnings, [
                      partnerId,
                      jobEarnings,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerEarnings>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i10.DailyEarning>>>
  getEarningsByDateRange(
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getEarningsByDateRange, [
              partnerId,
              startDate,
              endDate,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i10.DailyEarning>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i10.DailyEarning>>(
                    this,
                    Invocation.method(#getEarningsByDateRange, [
                      partnerId,
                      startDate,
                      endDate,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i10.DailyEarning>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  getPartnerAvailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAvailability, [partnerId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#getPartnerAvailability, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  updateAvailabilityStatus(
    String? partnerId,
    bool? isAvailable,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailabilityStatus, [
              partnerId,
              isAvailable,
              reason,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#updateAvailabilityStatus, [
                      partnerId,
                      isAvailable,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  updateOnlineStatus(String? partnerId, bool? isOnline) =>
      (super.noSuchMethod(
            Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#updateOnlineStatus, [
                      partnerId,
                      isOnline,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  updateWorkingHours(
    String? partnerId,
    Map<String, List<String>>? workingHours,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorkingHours, [partnerId, workingHours]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#updateWorkingHours, [
                      partnerId,
                      workingHours,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>> blockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#blockDates, [partnerId, dates]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#blockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>> unblockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unblockDates, [partnerId, dates]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#unblockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  setTemporaryUnavailability(
    String? partnerId,
    DateTime? unavailableUntil,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTemporaryUnavailability, [
              partnerId,
              unavailableUntil,
              reason,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#setTemporaryUnavailability, [
                      partnerId,
                      unavailableUntil,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>
  clearTemporaryUnavailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#clearTemporaryUnavailability, [partnerId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.PartnerAvailability>(
                    this,
                    Invocation.method(#clearTemporaryUnavailability, [
                      partnerId,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>> getJobStatistics(
    String? partnerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobStatistics,
              [partnerId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i5.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #getJobStatistics,
                      [partnerId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>
  getPerformanceMetrics(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i5.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#getPerformanceMetrics, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> markJobNotificationAsRead(
    String? partnerId,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markJobNotificationAsRead, [partnerId, jobId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#markJobNotificationAsRead, [
                  partnerId,
                  jobId,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, int>> getUnreadNotificationsCount(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotificationsCount, [partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, int>>.value(
              _FakeEither_0<_i5.Failure, int>(
                this,
                Invocation.method(#getUnreadNotificationsCount, [partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, int>>);
}

/// A class which mocks [NotificationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationService extends _i1.Mock
    implements _i11.NotificationService {
  MockNotificationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void setRepository(_i12.NotificationRepository? repository) =>
      super.noSuchMethod(
        Invocation.method(#setRepository, [repository]),
        returnValueForMissingStub: null,
      );

  @override
  void setUserPreferences(_i13.NotificationPreferences? preferences) =>
      super.noSuchMethod(
        Invocation.method(#setUserPreferences, [preferences]),
        returnValueForMissingStub: null,
      );

  @override
  void setActionHandler(_i14.NotificationActionHandler? actionHandler) =>
      super.noSuchMethod(
        Invocation.method(#setActionHandler, [actionHandler]),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> sendBookingNotification(
    String? fcmToken,
    String? title,
    String? body,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBookingNotification, [
              fcmToken,
              title,
              body,
              data,
            ]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#sendBookingNotification, [
                  fcmToken,
                  title,
                  body,
                  data,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<String?> getFCMToken() =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, []),
            returnValue: _i4.Future<String?>.value(),
          )
          as _i4.Future<String?>);

  @override
  _i4.Future<void> subscribeToTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [topic]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> unsubscribeFromTopic(String? topic) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [topic]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> subscribeToPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToPartnerNotifications, [partnerId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> unsubscribeFromPartnerNotifications(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromPartnerNotifications, [
              partnerId,
            ]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendNewJobNotification({
    required String? partnerId,
    required String? jobId,
    required String? serviceName,
    required String? clientName,
    required String? earnings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendNewJobNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #serviceName: serviceName,
              #clientName: clientName,
              #earnings: earnings,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendJobStatusNotification({
    required String? partnerId,
    required String? jobId,
    required String? status,
    required String? serviceName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendJobStatusNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #status: status,
              #serviceName: serviceName,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEarningsNotification({
    required String? partnerId,
    required String? amount,
    required String? period,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEarningsNotification, [], {
              #partnerId: partnerId,
              #amount: amount,
              #period: period,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendRatingNotification({
    required String? partnerId,
    required String? jobId,
    required double? rating,
    required String? review,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendRatingNotification, [], {
              #partnerId: partnerId,
              #jobId: jobId,
              #rating: rating,
              #review: review,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> scheduleNotification({
    required int? id,
    required String? title,
    required String? body,
    required DateTime? scheduledDate,
    Map<String, dynamic>? data,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #id: id,
              #title: title,
              #body: body,
              #scheduledDate: scheduledDate,
              #data: data,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> cancelScheduledNotification(int? id) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [id]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> cancelAllNotifications() =>
      (super.noSuchMethod(
            Invocation.method(#cancelAllNotifications, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> scheduleBookingReminder({
    required String? bookingId,
    required String? serviceName,
    required DateTime? scheduledDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleBookingReminder, [], {
              #bookingId: bookingId,
              #serviceName: serviceName,
              #scheduledDate: scheduledDate,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEnhancedBookingNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedBookingNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEnhancedJobNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedJobNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEnhancedPaymentNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.high,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEnhancedPaymentNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendSystemNotification({
    required String? userId,
    required String? title,
    required String? body,
    required Map<String, dynamic>? data,
    _i15.NotificationPriority? priority = _i15.NotificationPriority.normal,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSystemNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #priority: priority,
              #imageUrl: imageUrl,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

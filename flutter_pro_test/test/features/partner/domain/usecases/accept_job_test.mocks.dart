// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/partner/domain/usecases/accept_job_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i5;
import 'package:flutter_pro_test/features/partner/domain/entities/job.dart'
    as _i6;
import 'package:flutter_pro_test/features/partner/domain/entities/partner_earnings.dart'
    as _i7;
import 'package:flutter_pro_test/features/partner/domain/repositories/partner_job_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [PartnerJobRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerJobRepository extends _i1.Mock
    implements _i3.PartnerJobRepository {
  MockPartnerJobRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>> getPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPendingJobs, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Job>>(
                    this,
                    Invocation.method(#getPendingJobs, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>> getAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAcceptedJobs, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Job>>(
                    this,
                    Invocation.method(#getAcceptedJobs, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>> getJobHistory(
    String? partnerId, {
    _i6.JobStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobHistory,
              [partnerId],
              {
                #status: status,
                #startDate: startDate,
                #endDate: endDate,
                #limit: limit,
              },
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>.value(
                  _FakeEither_0<_i5.Failure, List<_i6.Job>>(
                    this,
                    Invocation.method(
                      #getJobHistory,
                      [partnerId],
                      {
                        #status: status,
                        #startDate: startDate,
                        #endDate: endDate,
                        #limit: limit,
                      },
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> getJobById(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [jobId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#getJobById, [jobId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> acceptJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#acceptJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#acceptJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> rejectJob(
    String? jobId,
    String? partnerId,
    String? rejectionReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rejectJob, [jobId, partnerId, rejectionReason]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#rejectJob, [
                  jobId,
                  partnerId,
                  rejectionReason,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> startJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#startJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> completeJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeJob, [jobId, partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#completeJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.Job>> cancelJob(
    String? jobId,
    String? partnerId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelJob, [
              jobId,
              partnerId,
              cancellationReason,
            ]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>.value(
              _FakeEither_0<_i5.Failure, _i6.Job>(
                this,
                Invocation.method(#cancelJob, [
                  jobId,
                  partnerId,
                  cancellationReason,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>> listenToPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToPendingJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>> listenToAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToAcceptedJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i6.Job>> listenToJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToJob, [jobId]),
            returnValue: _i4.Stream<_i2.Either<_i5.Failure, _i6.Job>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i6.Job>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>> listenToActiveJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToActiveJobs, [partnerId]),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, List<_i6.Job>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>> getPartnerEarnings(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerEarnings, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>>.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerEarnings>(
                    this,
                    Invocation.method(#getPartnerEarnings, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>>
  updatePartnerEarnings(String? partnerId, double? jobEarnings) =>
      (super.noSuchMethod(
            Invocation.method(#updatePartnerEarnings, [partnerId, jobEarnings]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>>.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerEarnings>(
                    this,
                    Invocation.method(#updatePartnerEarnings, [
                      partnerId,
                      jobEarnings,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerEarnings>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i7.DailyEarning>>>
  getEarningsByDateRange(
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getEarningsByDateRange, [
              partnerId,
              startDate,
              endDate,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i7.DailyEarning>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i7.DailyEarning>>(
                    this,
                    Invocation.method(#getEarningsByDateRange, [
                      partnerId,
                      startDate,
                      endDate,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i7.DailyEarning>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  getPartnerAvailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAvailability, [partnerId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#getPartnerAvailability, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  updateAvailabilityStatus(
    String? partnerId,
    bool? isAvailable,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailabilityStatus, [
              partnerId,
              isAvailable,
              reason,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#updateAvailabilityStatus, [
                      partnerId,
                      isAvailable,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  updateOnlineStatus(String? partnerId, bool? isOnline) =>
      (super.noSuchMethod(
            Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#updateOnlineStatus, [
                      partnerId,
                      isOnline,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  updateWorkingHours(
    String? partnerId,
    Map<String, List<String>>? workingHours,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorkingHours, [partnerId, workingHours]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#updateWorkingHours, [
                      partnerId,
                      workingHours,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>> blockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#blockDates, [partnerId, dates]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#blockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>> unblockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unblockDates, [partnerId, dates]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#unblockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  setTemporaryUnavailability(
    String? partnerId,
    DateTime? unavailableUntil,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTemporaryUnavailability, [
              partnerId,
              unavailableUntil,
              reason,
            ]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#setTemporaryUnavailability, [
                      partnerId,
                      unavailableUntil,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>
  clearTemporaryUnavailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#clearTemporaryUnavailability, [partnerId]),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i7.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i5.Failure, _i7.PartnerAvailability>(
                    this,
                    Invocation.method(#clearTemporaryUnavailability, [
                      partnerId,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.PartnerAvailability>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>> getJobStatistics(
    String? partnerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobStatistics,
              [partnerId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i5.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #getJobStatistics,
                      [partnerId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>
  getPerformanceMetrics(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, [partnerId]),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i5.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#getPerformanceMetrics, [partnerId]),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> markJobNotificationAsRead(
    String? partnerId,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markJobNotificationAsRead, [partnerId, jobId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#markJobNotificationAsRead, [
                  partnerId,
                  jobId,
                ]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, int>> getUnreadNotificationsCount(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotificationsCount, [partnerId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, int>>.value(
              _FakeEither_0<_i5.Failure, int>(
                this,
                Invocation.method(#getUnreadNotificationsCount, [partnerId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, int>>);
}

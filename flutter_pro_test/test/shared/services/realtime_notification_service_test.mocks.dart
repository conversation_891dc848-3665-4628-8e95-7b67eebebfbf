// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/shared/services/realtime_notification_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i6;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i3;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i15;
import 'package:flutter_pro_test/features/booking/domain/entities/booking.dart'
    as _i10;
import 'package:flutter_pro_test/features/booking/domain/entities/booking_request.dart'
    as _i11;
import 'package:flutter_pro_test/features/booking/domain/repositories/booking_repository.dart'
    as _i9;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification.dart'
    as _i7;
import 'package:flutter_pro_test/features/notifications/domain/entities/notification_preferences.dart'
    as _i8;
import 'package:flutter_pro_test/features/notifications/domain/repositories/notification_repository.dart'
    as _i4;
import 'package:flutter_pro_test/features/partner/domain/entities/job.dart'
    as _i13;
import 'package:flutter_pro_test/features/partner/domain/entities/partner_earnings.dart'
    as _i14;
import 'package:flutter_pro_test/features/partner/domain/repositories/partner_job_repository.dart'
    as _i12;
import 'package:flutter_pro_test/shared/services/notification_integration_service.dart'
    as _i16;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeAuthUser_1 extends _i1.SmartFake implements _i3.AuthUser {
  _FakeAuthUser_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [NotificationRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationRepository extends _i1.Mock
    implements _i4.NotificationRepository {
  MockNotificationRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  getUserNotifications(
    String? userId, {
    int? limit,
    String? lastNotificationId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserNotifications,
              [userId],
              {#limit: limit, #lastNotificationId: lastNotificationId},
            ),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i7.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getUserNotifications,
                      [userId],
                      {#limit: limit, #lastNotificationId: lastNotificationId},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  getUnreadNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotifications, [userId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i7.NotificationEntity>>(
                    this,
                    Invocation.method(#getUnreadNotifications, [userId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  getNotificationsByCategory(
    String? userId,
    _i7.NotificationCategory? category, {
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByCategory,
              [userId, category],
              {#limit: limit},
            ),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i7.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByCategory,
                      [userId, category],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  getNotificationsByType(String? userId, String? type, {int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationsByType,
              [userId, type],
              {#limit: limit},
            ),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i7.NotificationEntity>>(
                    this,
                    Invocation.method(
                      #getNotificationsByType,
                      [userId, type],
                      {#limit: limit},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>
  getNotificationById(String? notificationId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationById, [notificationId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i7.NotificationEntity>
                >.value(
                  _FakeEither_0<_i6.Failure, _i7.NotificationEntity>(
                    this,
                    Invocation.method(#getNotificationById, [notificationId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>
  createNotification(_i7.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#createNotification, [notification]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i7.NotificationEntity>
                >.value(
                  _FakeEither_0<_i6.Failure, _i7.NotificationEntity>(
                    this,
                    Invocation.method(#createNotification, [notification]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>
  updateNotification(_i7.NotificationEntity? notification) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotification, [notification]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i7.NotificationEntity>
                >.value(
                  _FakeEither_0<_i6.Failure, _i7.NotificationEntity>(
                    this,
                    Invocation.method(#updateNotification, [notification]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> deleteNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteNotification, [notificationId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#deleteNotification, [notificationId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>> markAsRead(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markAsRead, [notificationId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i7.NotificationEntity>
                >.value(
                  _FakeEither_0<_i6.Failure, _i7.NotificationEntity>(
                    this,
                    Invocation.method(#markAsRead, [notificationId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> markAllAsRead(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#markAllAsRead, [userId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#markAllAsRead, [userId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> deleteAllNotifications(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deleteAllNotifications, [userId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#deleteAllNotifications, [userId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  listenToUserNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUserNotifications, [userId]),
            returnValue:
                _i5.Stream<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, int>> listenToUnreadCount(
    String? userId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToUnreadCount, [userId]),
            returnValue: _i5.Stream<_i2.Either<_i6.Failure, int>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, int>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i8.NotificationPreferences>>
  getNotificationPreferences(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getNotificationPreferences, [userId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i8.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i6.Failure, _i8.NotificationPreferences>(
                    this,
                    Invocation.method(#getNotificationPreferences, [userId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i8.NotificationPreferences>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i8.NotificationPreferences>>
  updateNotificationPreferences(_i8.NotificationPreferences? preferences) =>
      (super.noSuchMethod(
            Invocation.method(#updateNotificationPreferences, [preferences]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i8.NotificationPreferences>
                >.value(
                  _FakeEither_0<_i6.Failure, _i8.NotificationPreferences>(
                    this,
                    Invocation.method(#updateNotificationPreferences, [
                      preferences,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i8.NotificationPreferences>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendPushNotification({
    required String? userId,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPushNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendPushNotification, [], {
                  #userId: userId,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendBulkPushNotification({
    required List<String>? userIds,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendBulkPushNotification, [], {
              #userIds: userIds,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendBulkPushNotification, [], {
                  #userIds: userIds,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendTopicNotification({
    required String? topic,
    required String? title,
    required String? body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendTopicNotification, [], {
              #topic: topic,
              #title: title,
              #body: body,
              #data: data,
              #imageUrl: imageUrl,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendTopicNotification, [], {
                  #topic: topic,
                  #title: title,
                  #body: body,
                  #data: data,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>
  scheduleNotification({
    required String? userId,
    required String? title,
    required String? body,
    required DateTime? scheduledAt,
    required String? type,
    Map<String, dynamic>? data,
    _i7.NotificationPriority? priority = _i7.NotificationPriority.normal,
    _i7.NotificationCategory? category = _i7.NotificationCategory.system,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#scheduleNotification, [], {
              #userId: userId,
              #title: title,
              #body: body,
              #scheduledAt: scheduledAt,
              #type: type,
              #data: data,
              #priority: priority,
              #category: category,
            }),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i7.NotificationEntity>
                >.value(
                  _FakeEither_0<_i6.Failure, _i7.NotificationEntity>(
                    this,
                    Invocation.method(#scheduleNotification, [], {
                      #userId: userId,
                      #title: title,
                      #body: body,
                      #scheduledAt: scheduledAt,
                      #type: type,
                      #data: data,
                      #priority: priority,
                      #category: category,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i7.NotificationEntity>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> cancelScheduledNotification(
    String? notificationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelScheduledNotification, [notificationId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#cancelScheduledNotification, [
                  notificationId,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>
  getScheduledNotifications(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getScheduledNotifications, [userId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i7.NotificationEntity>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i7.NotificationEntity>>(
                    this,
                    Invocation.method(#getScheduledNotifications, [userId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i7.NotificationEntity>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i4.NotificationStats>>
  getNotificationStats(
    String? userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getNotificationStats,
              [userId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i4.NotificationStats>
                >.value(
                  _FakeEither_0<_i6.Failure, _i4.NotificationStats>(
                    this,
                    Invocation.method(
                      #getNotificationStats,
                      [userId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i4.NotificationStats>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, int>> getUnreadCount(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCount, [userId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, int>>.value(
              _FakeEither_0<_i6.Failure, int>(
                this,
                Invocation.method(#getUnreadCount, [userId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, int>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, Map<_i7.NotificationCategory, int>>>
  getCountByCategory(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getCountByCategory, [userId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, Map<_i7.NotificationCategory, int>>
                >.value(
                  _FakeEither_0<
                    _i6.Failure,
                    Map<_i7.NotificationCategory, int>
                  >(this, Invocation.method(#getCountByCategory, [userId])),
                ),
          )
          as _i5.Future<
            _i2.Either<_i6.Failure, Map<_i7.NotificationCategory, int>>
          >);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> updateFCMToken(
    String? userId,
    String? token,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateFCMToken, [userId, token]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#updateFCMToken, [userId, token]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, String?>> getFCMToken(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#getFCMToken, [userId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, String?>>.value(
              _FakeEither_0<_i6.Failure, String?>(
                this,
                Invocation.method(#getFCMToken, [userId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, String?>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> subscribeToTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#subscribeToTopic, [userId, topic]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#subscribeToTopic, [userId, topic]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> unsubscribeFromTopic(
    String? userId,
    String? topic,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unsubscribeFromTopic, [userId, topic]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#unsubscribeFromTopic, [userId, topic]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);
}

/// A class which mocks [BookingRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockBookingRepository extends _i1.Mock implements _i9.BookingRepository {
  MockBookingRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> createBooking(
    _i11.BookingRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#createBooking, [request]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#createBooking, [request]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> getBookingById(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingById, [bookingId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#getBookingById, [bookingId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>> getUserBookings(
    String? userId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getUserBookings,
                      [userId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>> getPartnerBookings(
    String? partnerId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getPartnerBookings,
                      [partnerId],
                      {#status: status, #limit: limit},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>
  getBookingsByDateRange(
    String? userId,
    DateTime? startDate,
    DateTime? endDate, {
    bool? isPartner = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getBookingsByDateRange,
              [userId, startDate, endDate],
              {#isPartner: isPartner},
            ),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i10.Booking>>(
                    this,
                    Invocation.method(
                      #getBookingsByDateRange,
                      [userId, startDate, endDate],
                      {#isPartner: isPartner},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i10.Booking>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> updateBookingStatus(
    String? bookingId,
    _i10.BookingStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateBookingStatus, [bookingId, status]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#updateBookingStatus, [
                      bookingId,
                      status,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> cancelBooking(
    String? bookingId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelBooking, [bookingId, cancellationReason]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#cancelBooking, [
                      bookingId,
                      cancellationReason,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> confirmBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#confirmBooking, [bookingId, partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#confirmBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> startBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startBooking, [bookingId, partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#startBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>> completeBooking(
    String? bookingId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeBooking, [bookingId, partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>.value(
                  _FakeEither_0<_i6.Failure, _i10.Booking>(
                    this,
                    Invocation.method(#completeBooking, [bookingId, partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i10.Booking>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>> listenToUserBookings(
    String? userId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToUserBookings,
              [userId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>>
  listenToPartnerBookings(
    String? partnerId, {
    _i10.BookingStatus? status,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #listenToPartnerBookings,
              [partnerId],
              {#status: status, #limit: limit},
            ),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i10.Booking>>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, _i10.Booking>> listenToBooking(
    String? bookingId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToBooking, [bookingId]),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, _i10.Booking>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, _i10.Booking>>);
}

/// A class which mocks [PartnerJobRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockPartnerJobRepository extends _i1.Mock
    implements _i12.PartnerJobRepository {
  MockPartnerJobRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>> getPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPendingJobs, [partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i13.Job>>(
                    this,
                    Invocation.method(#getPendingJobs, [partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>> getAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAcceptedJobs, [partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i13.Job>>(
                    this,
                    Invocation.method(#getAcceptedJobs, [partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>> getJobHistory(
    String? partnerId, {
    _i13.JobStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 20,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobHistory,
              [partnerId],
              {
                #status: status,
                #startDate: startDate,
                #endDate: endDate,
                #limit: limit,
              },
            ),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>.value(
                  _FakeEither_0<_i6.Failure, List<_i13.Job>>(
                    this,
                    Invocation.method(
                      #getJobHistory,
                      [partnerId],
                      {
                        #status: status,
                        #startDate: startDate,
                        #endDate: endDate,
                        #limit: limit,
                      },
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> getJobById(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#getJobById, [jobId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#getJobById, [jobId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> acceptJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#acceptJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#acceptJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> rejectJob(
    String? jobId,
    String? partnerId,
    String? rejectionReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#rejectJob, [jobId, partnerId, rejectionReason]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#rejectJob, [
                  jobId,
                  partnerId,
                  rejectionReason,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> startJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#startJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#startJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> completeJob(
    String? jobId,
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#completeJob, [jobId, partnerId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#completeJob, [jobId, partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i13.Job>> cancelJob(
    String? jobId,
    String? partnerId,
    String? cancellationReason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#cancelJob, [
              jobId,
              partnerId,
              cancellationReason,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>.value(
              _FakeEither_0<_i6.Failure, _i13.Job>(
                this,
                Invocation.method(#cancelJob, [
                  jobId,
                  partnerId,
                  cancellationReason,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>> listenToPendingJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToPendingJobs, [partnerId]),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>> listenToAcceptedJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToAcceptedJobs, [partnerId]),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, _i13.Job>> listenToJob(String? jobId) =>
      (super.noSuchMethod(
            Invocation.method(#listenToJob, [jobId]),
            returnValue: _i5.Stream<_i2.Either<_i6.Failure, _i13.Job>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, _i13.Job>>);

  @override
  _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>> listenToActiveJobs(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#listenToActiveJobs, [partnerId]),
            returnValue:
                _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>.empty(),
          )
          as _i5.Stream<_i2.Either<_i6.Failure, List<_i13.Job>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>> getPartnerEarnings(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerEarnings, [partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>>.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerEarnings>(
                    this,
                    Invocation.method(#getPartnerEarnings, [partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>>
  updatePartnerEarnings(String? partnerId, double? jobEarnings) =>
      (super.noSuchMethod(
            Invocation.method(#updatePartnerEarnings, [partnerId, jobEarnings]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>>.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerEarnings>(
                    this,
                    Invocation.method(#updatePartnerEarnings, [
                      partnerId,
                      jobEarnings,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerEarnings>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, List<_i14.DailyEarning>>>
  getEarningsByDateRange(
    String? partnerId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getEarningsByDateRange, [
              partnerId,
              startDate,
              endDate,
            ]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, List<_i14.DailyEarning>>
                >.value(
                  _FakeEither_0<_i6.Failure, List<_i14.DailyEarning>>(
                    this,
                    Invocation.method(#getEarningsByDateRange, [
                      partnerId,
                      startDate,
                      endDate,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, List<_i14.DailyEarning>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  getPartnerAvailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAvailability, [partnerId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#getPartnerAvailability, [partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  updateAvailabilityStatus(
    String? partnerId,
    bool? isAvailable,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateAvailabilityStatus, [
              partnerId,
              isAvailable,
              reason,
            ]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#updateAvailabilityStatus, [
                      partnerId,
                      isAvailable,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  updateOnlineStatus(String? partnerId, bool? isOnline) =>
      (super.noSuchMethod(
            Invocation.method(#updateOnlineStatus, [partnerId, isOnline]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#updateOnlineStatus, [
                      partnerId,
                      isOnline,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  updateWorkingHours(
    String? partnerId,
    Map<String, List<String>>? workingHours,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateWorkingHours, [partnerId, workingHours]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#updateWorkingHours, [
                      partnerId,
                      workingHours,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>> blockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#blockDates, [partnerId, dates]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#blockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>> unblockDates(
    String? partnerId,
    List<String>? dates,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#unblockDates, [partnerId, dates]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#unblockDates, [partnerId, dates]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  setTemporaryUnavailability(
    String? partnerId,
    DateTime? unavailableUntil,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#setTemporaryUnavailability, [
              partnerId,
              unavailableUntil,
              reason,
            ]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#setTemporaryUnavailability, [
                      partnerId,
                      unavailableUntil,
                      reason,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>
  clearTemporaryUnavailability(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#clearTemporaryUnavailability, [partnerId]),
            returnValue:
                _i5.Future<
                  _i2.Either<_i6.Failure, _i14.PartnerAvailability>
                >.value(
                  _FakeEither_0<_i6.Failure, _i14.PartnerAvailability>(
                    this,
                    Invocation.method(#clearTemporaryUnavailability, [
                      partnerId,
                    ]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i14.PartnerAvailability>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>> getJobStatistics(
    String? partnerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getJobStatistics,
              [partnerId],
              {#startDate: startDate, #endDate: endDate},
            ),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i6.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(
                      #getJobStatistics,
                      [partnerId],
                      {#startDate: startDate, #endDate: endDate},
                    ),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>>
  getPerformanceMetrics(String? partnerId) =>
      (super.noSuchMethod(
            Invocation.method(#getPerformanceMetrics, [partnerId]),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i6.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#getPerformanceMetrics, [partnerId]),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, Map<String, dynamic>>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> markJobNotificationAsRead(
    String? partnerId,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#markJobNotificationAsRead, [partnerId, jobId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#markJobNotificationAsRead, [
                  partnerId,
                  jobId,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, int>> getUnreadNotificationsCount(
    String? partnerId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadNotificationsCount, [partnerId]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, int>>.value(
              _FakeEither_0<_i6.Failure, int>(
                this,
                Invocation.method(#getUnreadNotificationsCount, [partnerId]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, int>>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i15.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i3.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i5.Stream<_i3.AuthUser>.empty(),
          )
          as _i5.Stream<_i3.AuthUser>);

  @override
  _i3.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_1(this, Invocation.getter(#currentUser)),
          )
          as _i3.AuthUser);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i6.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i6.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, String>>.value(
              _FakeEither_0<_i6.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, String>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i6.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, bool>>.value(
              _FakeEither_0<_i6.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, bool>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>.value(
                  _FakeEither_0<_i6.Failure, _i3.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, _i3.AuthUser>>);
}

/// A class which mocks [NotificationIntegrationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockNotificationIntegrationService extends _i1.Mock
    implements _i16.NotificationIntegrationService {
  MockNotificationIntegrationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingCreated(
    _i10.Booking? booking,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyBookingCreated, [booking]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyBookingCreated, [booking]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingConfirmed(
    _i10.Booking? booking,
    String? partnerName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyBookingConfirmed, [booking, partnerName]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyBookingConfirmed, [
                  booking,
                  partnerName,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingStarted(
    _i10.Booking? booking,
    String? partnerName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyBookingStarted, [booking, partnerName]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyBookingStarted, [
                  booking,
                  partnerName,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingCompleted(
    _i10.Booking? booking,
    String? partnerName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyBookingCompleted, [booking, partnerName]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyBookingCompleted, [
                  booking,
                  partnerName,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingCancelled(
    _i10.Booking? booking,
    String? cancellationReason, {
    String? cancelledBy,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #notifyBookingCancelled,
              [booking, cancellationReason],
              {#cancelledBy: cancelledBy},
            ),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(
                  #notifyBookingCancelled,
                  [booking, cancellationReason],
                  {#cancelledBy: cancelledBy},
                ),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyBookingReminder(
    _i10.Booking? booking,
    String? partnerName, {
    int? hoursBeforeService = 2,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #notifyBookingReminder,
              [booking, partnerName],
              {#hoursBeforeService: hoursBeforeService},
            ),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(
                  #notifyBookingReminder,
                  [booking, partnerName],
                  {#hoursBeforeService: hoursBeforeService},
                ),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyNewJobAvailable(
    _i13.Job? job,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyNewJobAvailable, [job]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyNewJobAvailable, [job]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyJobAccepted(_i13.Job? job) =>
      (super.noSuchMethod(
            Invocation.method(#notifyJobAccepted, [job]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyJobAccepted, [job]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyEarningsUpdate(
    String? partnerId,
    double? newEarnings,
    double? totalEarnings,
    String? jobId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyEarningsUpdate, [
              partnerId,
              newEarnings,
              totalEarnings,
              jobId,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyEarningsUpdate, [
                  partnerId,
                  newEarnings,
                  totalEarnings,
                  jobId,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyPaymentReceived(
    String? userId,
    double? amount,
    String? bookingId,
    String? serviceName,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyPaymentReceived, [
              userId,
              amount,
              bookingId,
              serviceName,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyPaymentReceived, [
                  userId,
                  amount,
                  bookingId,
                  serviceName,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifyPaymentFailed(
    String? userId,
    double? amount,
    String? bookingId,
    String? serviceName,
    String? reason,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifyPaymentFailed, [
              userId,
              amount,
              bookingId,
              serviceName,
              reason,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifyPaymentFailed, [
                  userId,
                  amount,
                  bookingId,
                  serviceName,
                  reason,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> notifySystemMaintenance(
    String? userId,
    String? title,
    String? message,
    DateTime? scheduledTime,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#notifySystemMaintenance, [
              userId,
              title,
              message,
              scheduledTime,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#notifySystemMaintenance, [
                  userId,
                  title,
                  message,
                  scheduledTime,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i2.Either<_i6.Failure, void>> sendBulkNotifications(
    List<String>? userIds,
    String? title,
    String? body,
    String? type,
    _i7.NotificationCategory? category,
    _i7.NotificationPriority? priority,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#sendBulkNotifications, [
              userIds,
              title,
              body,
              type,
              category,
              priority,
              data,
            ]),
            returnValue: _i5.Future<_i2.Either<_i6.Failure, void>>.value(
              _FakeEither_0<_i6.Failure, void>(
                this,
                Invocation.method(#sendBulkNotifications, [
                  userIds,
                  title,
                  body,
                  type,
                  category,
                  priority,
                  data,
                ]),
              ),
            ),
          )
          as _i5.Future<_i2.Either<_i6.Failure, void>>);
}

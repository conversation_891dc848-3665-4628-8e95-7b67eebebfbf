// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/auth/domain/usecases/sign_in_with_email_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dartz/dartz.dart' as _i3;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i6;
import 'package:flutter_pro_test/features/auth/domain/entities/auth_user.dart'
    as _i2;
import 'package:flutter_pro_test/features/auth/domain/repositories/auth_repository.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthUser_0 extends _i1.SmartFake implements _i2.AuthUser {
  _FakeAuthUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeEither_1<L, R> extends _i1.SmartFake implements _i3.Either<L, R> {
  _FakeEither_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i4.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i2.AuthUser> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i5.Stream<_i2.AuthUser>.empty(),
          )
          as _i5.Stream<_i2.AuthUser>);

  @override
  _i2.AuthUser get currentUser =>
      (super.noSuchMethod(
            Invocation.getter(#currentUser),
            returnValue: _FakeAuthUser_0(this, Invocation.getter(#currentUser)),
          )
          as _i2.AuthUser);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i6.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signInWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>> signUpWithEmailAndPassword({
    required String? email,
    required String? password,
    required String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signUpWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i6.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#signUpWithEmailAndPassword, [], {
                      #email: email,
                      #password: password,
                      #displayName: displayName,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, String>> signInWithPhoneNumber({
    required String? phoneNumber,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [], {
              #phoneNumber: phoneNumber,
            }),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, String>>.value(
              _FakeEither_1<_i6.Failure, String>(
                this,
                Invocation.method(#signInWithPhoneNumber, [], {
                  #phoneNumber: phoneNumber,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, String>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>> verifyPhoneNumber({
    required String? verificationId,
    required String? smsCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #verificationId: verificationId,
              #smsCode: smsCode,
            }),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i6.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#verifyPhoneNumber, [], {
                      #verificationId: verificationId,
                      #smsCode: smsCode,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#sendEmailVerification, []),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> sendPasswordResetEmail({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#sendPasswordResetEmail, [], {#email: email}),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> updateProfile({
    String? displayName,
    String? photoURL,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#updateProfile, [], {
                  #displayName: displayName,
                  #photoURL: photoURL,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> updateEmail({
    required String? newEmail,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#updateEmail, [], {#newEmail: newEmail}),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#updatePassword, [], {
                  #currentPassword: currentPassword,
                  #newPassword: newPassword,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> reauthenticateWithPassword({
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPassword, [], {
              #password: password,
            }),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#reauthenticateWithPassword, [], {
                  #password: password,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#signOut, []),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, void>> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, void>>.value(
              _FakeEither_1<_i6.Failure, void>(
                this,
                Invocation.method(#deleteAccount, []),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, void>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, bool>> isEmailInUse({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#isEmailInUse, [], {#email: email}),
            returnValue: _i5.Future<_i3.Either<_i6.Failure, bool>>.value(
              _FakeEither_1<_i6.Failure, bool>(
                this,
                Invocation.method(#isEmailInUse, [], {#email: email}),
              ),
            ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, bool>>);

  @override
  _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue:
                _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>.value(
                  _FakeEither_1<_i6.Failure, _i2.AuthUser>(
                    this,
                    Invocation.method(#refreshUser, []),
                  ),
                ),
          )
          as _i5.Future<_i3.Either<_i6.Failure, _i2.AuthUser>>);
}

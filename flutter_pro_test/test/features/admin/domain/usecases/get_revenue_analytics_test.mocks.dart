// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in flutter_pro_test/test/features/admin/domain/usecases/get_revenue_analytics_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:flutter_pro_test/core/errors/failures.dart' as _i5;
import 'package:flutter_pro_test/features/admin/domain/entities/booking_analytics.dart'
    as _i7;
import 'package:flutter_pro_test/features/admin/domain/entities/partner_analytics.dart'
    as _i8;
import 'package:flutter_pro_test/features/admin/domain/entities/report_config.dart'
    as _i11;
import 'package:flutter_pro_test/features/admin/domain/entities/revenue_analytics.dart'
    as _i10;
import 'package:flutter_pro_test/features/admin/domain/entities/system_metrics.dart'
    as _i6;
import 'package:flutter_pro_test/features/admin/domain/entities/user_analytics.dart'
    as _i9;
import 'package:flutter_pro_test/features/admin/domain/repositories/analytics_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AnalyticsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAnalyticsRepository extends _i1.Mock
    implements _i3.AnalyticsRepository {
  MockAnalyticsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i6.SystemMetrics>> getSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemMetrics, []),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i6.SystemMetrics>>.value(
                  _FakeEither_0<_i5.Failure, _i6.SystemMetrics>(
                    this,
                    Invocation.method(#getSystemMetrics, []),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i6.SystemMetrics>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i6.SystemMetrics>> watchSystemMetrics() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemMetrics, []),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, _i6.SystemMetrics>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i6.SystemMetrics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i7.BookingAnalytics>>
  getBookingAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    String? serviceId,
    String? partnerId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getBookingAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #serviceId: serviceId,
              #partnerId: partnerId,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i7.BookingAnalytics>>.value(
                  _FakeEither_0<_i5.Failure, _i7.BookingAnalytics>(
                    this,
                    Invocation.method(#getBookingAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #serviceId: serviceId,
                      #partnerId: partnerId,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i7.BookingAnalytics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i8.PartnerAnalytics>>
  getPartnerAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includePerformanceDetails = false,
    bool? includeQualityMetrics = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getPartnerAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includePerformanceDetails: includePerformanceDetails,
              #includeQualityMetrics: includeQualityMetrics,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i8.PartnerAnalytics>>.value(
                  _FakeEither_0<_i5.Failure, _i8.PartnerAnalytics>(
                    this,
                    Invocation.method(#getPartnerAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includePerformanceDetails: includePerformanceDetails,
                      #includeQualityMetrics: includeQualityMetrics,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i8.PartnerAnalytics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i9.UserAnalytics>> getUserAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeCohortAnalysis = false,
    bool? includeSegmentation = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getUserAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeCohortAnalysis: includeCohortAnalysis,
              #includeSegmentation: includeSegmentation,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i9.UserAnalytics>>.value(
                  _FakeEither_0<_i5.Failure, _i9.UserAnalytics>(
                    this,
                    Invocation.method(#getUserAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeCohortAnalysis: includeCohortAnalysis,
                      #includeSegmentation: includeSegmentation,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i9.UserAnalytics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i10.RevenueAnalytics>>
  getRevenueAnalytics({
    required DateTime? startDate,
    required DateTime? endDate,
    bool? includeForecasts = false,
    bool? includeComparisons = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getRevenueAnalytics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #includeForecasts: includeForecasts,
              #includeComparisons: includeComparisons,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i10.RevenueAnalytics>
                >.value(
                  _FakeEither_0<_i5.Failure, _i10.RevenueAnalytics>(
                    this,
                    Invocation.method(#getRevenueAnalytics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #includeForecasts: includeForecasts,
                      #includeComparisons: includeComparisons,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i10.RevenueAnalytics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i3.SystemHealth>> getSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#getSystemHealth, []),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i3.SystemHealth>>.value(
                  _FakeEither_0<_i5.Failure, _i3.SystemHealth>(
                    this,
                    Invocation.method(#getSystemHealth, []),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i3.SystemHealth>>);

  @override
  _i4.Stream<_i2.Either<_i5.Failure, _i3.SystemHealth>> watchSystemHealth() =>
      (super.noSuchMethod(
            Invocation.method(#watchSystemHealth, []),
            returnValue:
                _i4.Stream<_i2.Either<_i5.Failure, _i3.SystemHealth>>.empty(),
          )
          as _i4.Stream<_i2.Either<_i5.Failure, _i3.SystemHealth>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, String>> exportAnalyticsData({
    required _i3.AnalyticsExportType? type,
    required DateTime? startDate,
    required DateTime? endDate,
    required _i3.AnalyticsExportFormat? format,
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#exportAnalyticsData, [], {
              #type: type,
              #startDate: startDate,
              #endDate: endDate,
              #format: format,
              #filters: filters,
            }),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, String>>.value(
              _FakeEither_0<_i5.Failure, String>(
                this,
                Invocation.method(#exportAnalyticsData, [], {
                  #type: type,
                  #startDate: startDate,
                  #endDate: endDate,
                  #format: format,
                  #filters: filters,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, String>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i3.AnalyticsSummary>>
  getAnalyticsSummary({
    required DateTime? startDate,
    required DateTime? endDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsSummary, [], {
              #startDate: startDate,
              #endDate: endDate,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i3.AnalyticsSummary>>.value(
                  _FakeEither_0<_i5.Failure, _i3.AnalyticsSummary>(
                    this,
                    Invocation.method(#getAnalyticsSummary, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i3.AnalyticsSummary>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i3.ComparativeAnalytics>>
  getComparativeAnalytics({
    required DateTime? currentStart,
    required DateTime? currentEnd,
    required DateTime? previousStart,
    required DateTime? previousEnd,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getComparativeAnalytics, [], {
              #currentStart: currentStart,
              #currentEnd: currentEnd,
              #previousStart: previousStart,
              #previousEnd: previousEnd,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i3.ComparativeAnalytics>
                >.value(
                  _FakeEither_0<_i5.Failure, _i3.ComparativeAnalytics>(
                    this,
                    Invocation.method(#getComparativeAnalytics, [], {
                      #currentStart: currentStart,
                      #currentEnd: currentEnd,
                      #previousStart: previousStart,
                      #previousEnd: previousEnd,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i3.ComparativeAnalytics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i3.TopPerformingMetrics>>
  getTopPerformingMetrics({
    required DateTime? startDate,
    required DateTime? endDate,
    int? limit = 10,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTopPerformingMetrics, [], {
              #startDate: startDate,
              #endDate: endDate,
              #limit: limit,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, _i3.TopPerformingMetrics>
                >.value(
                  _FakeEither_0<_i5.Failure, _i3.TopPerformingMetrics>(
                    this,
                    Invocation.method(#getTopPerformingMetrics, [], {
                      #startDate: startDate,
                      #endDate: endDate,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i3.TopPerformingMetrics>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i3.AnalyticsAlert>>>
  getAnalyticsAlerts({
    _i3.AnalyticsAlertSeverity? severity,
    bool? unreadOnly = false,
    int? limit = 50,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAnalyticsAlerts, [], {
              #severity: severity,
              #unreadOnly: unreadOnly,
              #limit: limit,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i3.AnalyticsAlert>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i3.AnalyticsAlert>>(
                    this,
                    Invocation.method(#getAnalyticsAlerts, [], {
                      #severity: severity,
                      #unreadOnly: unreadOnly,
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i3.AnalyticsAlert>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> markAlertAsRead(String? alertId) =>
      (super.noSuchMethod(
            Invocation.method(#markAlertAsRead, [alertId]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#markAlertAsRead, [alertId]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>> executeCustomQuery({
    required String? query,
    Map<String, dynamic>? parameters,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeCustomQuery, [], {
              #query: query,
              #parameters: parameters,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>.value(
                  _FakeEither_0<_i5.Failure, Map<String, dynamic>>(
                    this,
                    Invocation.method(#executeCustomQuery, [], {
                      #query: query,
                      #parameters: parameters,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, Map<String, dynamic>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i11.GeneratedReport>> generateReport({
    required _i11.ReportConfig? config,
    Map<String, dynamic>? customData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#generateReport, [], {
              #config: config,
              #customData: customData,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i11.GeneratedReport>>.value(
                  _FakeEither_0<_i5.Failure, _i11.GeneratedReport>(
                    this,
                    Invocation.method(#generateReport, [], {
                      #config: config,
                      #customData: customData,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i11.GeneratedReport>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i11.ReportConfig>>>
  getReportConfigs() =>
      (super.noSuchMethod(
            Invocation.method(#getReportConfigs, []),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i11.ReportConfig>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i11.ReportConfig>>(
                    this,
                    Invocation.method(#getReportConfigs, []),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i11.ReportConfig>>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>> createReportConfig({
    required String? name,
    required String? description,
    required _i11.ReportType? type,
    required _i11.ReportFormat? format,
    required DateTime? startDate,
    required DateTime? endDate,
    required List<String>? metrics,
    required List<String>? dimensions,
    List<_i11.ReportFilter>? filters = const [],
    _i11.ReportSchedule? schedule,
    List<String>? recipients = const [],
    Map<String, dynamic>? customSettings = const {},
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createReportConfig, [], {
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>>.value(
                  _FakeEither_0<_i5.Failure, _i11.ReportConfig>(
                    this,
                    Invocation.method(#createReportConfig, [], {
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>> updateReportConfig({
    required String? id,
    String? name,
    String? description,
    _i11.ReportType? type,
    _i11.ReportFormat? format,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? metrics,
    List<String>? dimensions,
    List<_i11.ReportFilter>? filters,
    _i11.ReportSchedule? schedule,
    List<String>? recipients,
    Map<String, dynamic>? customSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateReportConfig, [], {
              #id: id,
              #name: name,
              #description: description,
              #type: type,
              #format: format,
              #startDate: startDate,
              #endDate: endDate,
              #metrics: metrics,
              #dimensions: dimensions,
              #filters: filters,
              #schedule: schedule,
              #recipients: recipients,
              #customSettings: customSettings,
            }),
            returnValue:
                _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>>.value(
                  _FakeEither_0<_i5.Failure, _i11.ReportConfig>(
                    this,
                    Invocation.method(#updateReportConfig, [], {
                      #id: id,
                      #name: name,
                      #description: description,
                      #type: type,
                      #format: format,
                      #startDate: startDate,
                      #endDate: endDate,
                      #metrics: metrics,
                      #dimensions: dimensions,
                      #filters: filters,
                      #schedule: schedule,
                      #recipients: recipients,
                      #customSettings: customSettings,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, _i11.ReportConfig>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, void>> deleteReportConfig(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteReportConfig, [id]),
            returnValue: _i4.Future<_i2.Either<_i5.Failure, void>>.value(
              _FakeEither_0<_i5.Failure, void>(
                this,
                Invocation.method(#deleteReportConfig, [id]),
              ),
            ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, void>>);

  @override
  _i4.Future<_i2.Either<_i5.Failure, List<_i11.GeneratedReport>>>
  getGeneratedReports({String? configId, int? limit = 20, int? offset = 0}) =>
      (super.noSuchMethod(
            Invocation.method(#getGeneratedReports, [], {
              #configId: configId,
              #limit: limit,
              #offset: offset,
            }),
            returnValue:
                _i4.Future<
                  _i2.Either<_i5.Failure, List<_i11.GeneratedReport>>
                >.value(
                  _FakeEither_0<_i5.Failure, List<_i11.GeneratedReport>>(
                    this,
                    Invocation.method(#getGeneratedReports, [], {
                      #configId: configId,
                      #limit: limit,
                      #offset: offset,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Either<_i5.Failure, List<_i11.GeneratedReport>>>);
}
